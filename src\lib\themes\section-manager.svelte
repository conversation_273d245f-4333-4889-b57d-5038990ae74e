<script lang="ts">
  // Complete Section Manager - Orchestrates sections based on theme configuration
  
  import { onMount, onDestroy } from 'svelte'
  import type { SectionConfig, SectionData, ThemeConfig } from './types'
  
  // Import all section components
  import Banners from '$lib/components/home/<USER>'
  import Collections from '$lib/components/home/<USER>'
  import HomepageCategoryListWithImage from '$lib/components/home/<USER>'
  import HomepageBanners from '$lib/components/home/<USER>'
  import BrandsCarousel from '$lib/components/home/<USER>'
  import FeaturedProductsGrid from '$lib/components/product-catalogue/featured-products-grid.svelte'
  
  // Import enhanced section components (will create these next)
  import EnhancedBanners from '$lib/components/home/<USER>'
  import EnhancedCollections from '$lib/components/home/<USER>'
  import EnhancedProductGrid from '$lib/components/product-catalogue/enhanced-product-grid.svelte'
  import EnhancedCategories from '$lib/components/home/<USER>'
  
  // Props
  let { 
    theme = 'default',
    sections = [],
    data = {},
    className = '',
    ...props 
  }: {
    theme?: string
    sections?: SectionConfig[]
    data?: SectionData
    className?: string
    [key: string]: any
  } = $props()
  
  // Component registry - maps component names to actual components
  const componentRegistry = {
    // Original components
    'banners': {
      default: Banners,
      original: Banners
    },
    'collections': {
      default: Collections,
      original: Collections,
      enhanced: EnhancedCollections,
      luxury: EnhancedCollections,
      minimal: EnhancedCollections,
      fashion: EnhancedCollections,
      tech: EnhancedCollections
    },
    'categories': {
      default: HomepageCategoryListWithImage,
      original: HomepageCategoryListWithImage,
      enhanced: EnhancedCategories,
      luxury: EnhancedCategories,
      minimal: EnhancedCategories,
      fashion: EnhancedCategories,
      tech: EnhancedCategories
    },
    'homepage-banners': {
      default: HomepageBanners,
      original: HomepageBanners
    },
    'featured-products': {
      default: FeaturedProductsGrid,
      original: FeaturedProductsGrid,
      enhanced: EnhancedProductGrid,
      luxury: EnhancedProductGrid,
      minimal: EnhancedProductGrid,
      fashion: EnhancedProductGrid,
      tech: EnhancedProductGrid,
      masonry: EnhancedProductGrid
    },
    'brands': {
      default: BrandsCarousel,
      original: BrandsCarousel
    },
    'hero': {
      default: EnhancedBanners,
      enhanced: EnhancedBanners,
      luxury: EnhancedBanners,
      minimal: EnhancedBanners,
      fashion: EnhancedBanners,
      tech: EnhancedBanners
    }
  }
  
  // State
  let loadingComponents = $state(new Set<string>())
  let errorComponents = $state(new Set<string>())
  let mountedSections = $state<string[]>([])
  
  // Reactive sections - filter and sort using runes
  let visibleSections = $derived(sections
    .filter(section => section.visible !== false)
    .sort((a, b) => (a.order || 0) - (b.order || 0)))
  
  // Get component for a section
  function getComponent(section: SectionConfig) {
    const componentMap = componentRegistry[section.component as keyof typeof componentRegistry]
    
    if (!componentMap) {
      console.warn(`Component "${section.component}" not found in registry`)
      return null
    }
    
    // Try to get variant-specific component, fallback to default
    const component = componentMap[section.variant as keyof typeof componentMap] || 
                     componentMap.enhanced || 
                     componentMap.default
    
    if (!component) {
      console.warn(`Variant "${section.variant}" not found for component "${section.component}"`)
      return componentMap.default || null
    }
    
    return component
  }
  
  // Get props for a section
  function getSectionProps(section: SectionConfig) {
    const baseProps = {
      theme,
      variant: section.variant,
      ...section.props
    }
    
    // Add component-specific data
    switch (section.component) {
      case 'banners':
      case 'hero':
        return {
          ...baseProps,
          sliderBannersDesktop: data.page?.desktopBanners || data.banners,
          sliderBannersMobile: data.page?.mobileBanners || data.banners
        }
      
      case 'collections':
        return {
          ...baseProps,
          collections: data.collections
        }
      
      case 'featured-products':
        return {
          ...baseProps,
          data: data.featuredProducts || [],
          displayProduct: data.displayProduct,
          loadMore: data.loadMore,
          loading: data.loading
        }
      
      case 'categories':
        return {
          ...baseProps,
          categories: data.featuredCategories || [],
          loading: data.loading
        }
      
      case 'homepage-banners':
        return {
          ...baseProps,
          bannersList: data.page?.sections || []
        }
      
      case 'brands':
        return {
          ...baseProps,
          brands: data.brands || []
        }
      
      default:
        return baseProps
    }
  }
  
  // Handle component loading
  function handleComponentLoad(sectionId: string) {
    loadingComponents.delete(sectionId)
    loadingComponents = new Set(loadingComponents)
    
    if (!mountedSections.includes(sectionId)) {
      mountedSections = [...mountedSections, sectionId]
    }
  }
  
  // Handle component error
  function handleComponentError(sectionId: string, error: any) {
    console.error(`Error loading section ${sectionId}:`, error)
    errorComponents.add(sectionId)
    errorComponents = new Set(errorComponents)
    loadingComponents.delete(sectionId)
    loadingComponents = new Set(loadingComponents)
  }
  
  // Check if section should show loading
  function isLoading(sectionId: string): boolean {
    return loadingComponents.has(sectionId)
  }
  
  // Check if section has error
  function hasError(sectionId: string): boolean {
    return errorComponents.has(sectionId)
  }
  
  // Get responsive props for section
  function getResponsiveProps(section: SectionConfig) {
    if (!section.responsive) return {}
    
    const responsive = section.responsive
    const props: any = {}
    
    // Add responsive classes or props based on screen size
    if (typeof window !== 'undefined') {
      const width = window.innerWidth
      
      if (width < 768 && responsive.mobile) {
        Object.assign(props, responsive.mobile.props || {})
      } else if (width < 1024 && responsive.tablet) {
        Object.assign(props, responsive.tablet.props || {})
      } else if (responsive.desktop) {
        Object.assign(props, responsive.desktop.props || {})
      }
    }
    
    return props
  }
  
  // Lifecycle
  onMount(() => {
    // Initialize loading state for all sections
    visibleSections.forEach(section => {
      loadingComponents.add(section.id)
    })
    loadingComponents = new Set(loadingComponents)
    
    // Apply theme-specific styles
    if (typeof document !== 'undefined') {
      document.body.classList.add(`theme-${theme}`)
    }
  })
  
  onDestroy(() => {
    // Cleanup theme classes
    if (typeof document !== 'undefined') {
      document.body.classList.remove(`theme-${theme}`)
    }
  })
  
  // Watch for section changes
  $effect(() => {
    // Reset state when sections change
    loadingComponents.clear()
    errorComponents.clear()
    mountedSections = []
    
    visibleSections.forEach(section => {
      loadingComponents.add(section.id)
    })
    loadingComponents = new Set(loadingComponents)
  })
</script>

<!-- Section Manager Container -->
<div class="section-manager theme-{theme} {className}" data-theme={theme}>
  {#each visibleSections as section (section.id)}
    {@const Component = getComponent(section)}
    {@const sectionProps = getSectionProps(section)}
    {@const responsiveProps = getResponsiveProps(section)}
    
    <section 
      class="section section-{section.component} section-{section.id} variant-{section.variant}"
      data-section-id={section.id}
      data-component={section.component}
      data-variant={section.variant}
      data-theme={theme}
    >
      {#if Component}
        {#if isLoading(section.id)}
          <!-- Loading skeleton -->
          <div class="section-loading" data-section={section.id}>
            <div class="skeleton-container">
              <div class="skeleton-header"></div>
              <div class="skeleton-content">
                <div class="skeleton-line"></div>
                <div class="skeleton-line"></div>
                <div class="skeleton-line short"></div>
              </div>
            </div>
          </div>
        {/if}
        
        {#if hasError(section.id)}
          <!-- Error state -->
          <div class="section-error" data-section={section.id}>
            <div class="error-container">
              <div class="error-icon">⚠️</div>
              <h3>Failed to load section</h3>
              <p>Section "{section.component}" could not be loaded.</p>
              <button 
                class="retry-button"
                onclick={() => {
                  errorComponents.delete(section.id)
                  errorComponents = new Set(errorComponents)
                  loadingComponents.add(section.id)
                  loadingComponents = new Set(loadingComponents)
                }}
              >
                Retry
              </button>
            </div>
          </div>
        {:else}
          <!-- Render component -->
          <svelte:component 
            this={Component}
            {...sectionProps}
            {...responsiveProps}
            {...props}
            onload={() => handleComponentLoad(section.id)}
            onerror={(error) => handleComponentError(section.id, error)}
          />
        {/if}
      {:else}
        <!-- Component not found -->
        <div class="section-not-found" data-section={section.id}>
          <div class="not-found-container">
            <div class="not-found-icon">🔍</div>
            <h3>Component not found</h3>
            <p>Component "{section.component}" with variant "{section.variant}" is not available.</p>
            <details>
              <summary>Debug Info</summary>
              <pre>{JSON.stringify(section, null, 2)}</pre>
            </details>
          </div>
        </div>
      {/if}
    </section>
  {/each}
  
  {#if visibleSections.length === 0}
    <!-- Empty state -->
    <div class="section-manager-empty">
      <div class="empty-container">
        <div class="empty-icon">📄</div>
        <h3>No sections configured</h3>
        <p>This theme doesn't have any sections configured yet.</p>
      </div>
    </div>
  {/if}
</div>

<style>
  .section-manager {
    min-height: 100vh;
    position: relative;
  }
  
  .section {
    position: relative;
    width: 100%;
  }
  
  /* Loading states */
  .section-loading {
    padding: 2rem;
    background: #f8f9fa;
  }
  
  .skeleton-container {
    max-width: 1200px;
    margin: 0 auto;
    animation: pulse 1.5s ease-in-out infinite;
  }
  
  .skeleton-header {
    height: 200px;
    background: #e2e8f0;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
  }
  
  .skeleton-content {
    space-y: 0.75rem;
  }
  
  .skeleton-line {
    height: 1rem;
    background: #e2e8f0;
    border-radius: 0.25rem;
    margin-bottom: 0.75rem;
  }
  
  .skeleton-line.short {
    width: 60%;
  }
  
  /* Error states */
  .section-error {
    padding: 2rem;
    background: #fef2f2;
    border: 1px solid #fecaca;
  }
  
  .error-container {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
  }
  
  .error-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }
  
  .error-container h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #dc2626;
    margin-bottom: 0.5rem;
  }
  
  .error-container p {
    color: #7f1d1d;
    margin-bottom: 1rem;
  }
  
  .retry-button {
    background: #dc2626;
    color: white;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.25rem;
    cursor: pointer;
    font-weight: 500;
  }
  
  .retry-button:hover {
    background: #b91c1c;
  }
  
  /* Not found states */
  .section-not-found {
    padding: 2rem;
    background: #fffbeb;
    border: 1px solid #fed7aa;
  }
  
  .not-found-container {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
  }
  
  .not-found-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }
  
  .not-found-container h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #d97706;
    margin-bottom: 0.5rem;
  }
  
  .not-found-container p {
    color: #92400e;
    margin-bottom: 1rem;
  }
  
  .not-found-container details {
    text-align: left;
    background: white;
    padding: 1rem;
    border-radius: 0.25rem;
    border: 1px solid #fed7aa;
  }
  
  .not-found-container pre {
    font-size: 0.75rem;
    overflow-x: auto;
  }
  
  /* Empty state */
  .section-manager-empty {
    min-height: 50vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
  }
  
  .empty-container {
    text-align: center;
    max-width: 400px;
  }
  
  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
  }
  
  .empty-container h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }
  
  .empty-container p {
    color: #6b7280;
  }
  
  /* Animations */
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
  
  /* Theme-specific styles */
  .theme-default {
    background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
  }

  .theme-luxury {
    background: linear-gradient(180deg, #ffffff 0%, #fefbf3 100%);
  }
  
  .theme-minimal {
    background: #ffffff;
  }
  
  .theme-fashion {
    background: linear-gradient(135deg, #fdf2f8 0%, #f3e8ff 100%);
  }
  
  .theme-tech {
    background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  }
  
  /* Responsive */
  @media (max-width: 768px) {
    .section-loading,
    .section-error,
    .section-not-found {
      padding: 1rem;
    }
    
    .skeleton-header {
      height: 150px;
    }
  }
</style>
