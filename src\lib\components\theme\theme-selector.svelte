<script lang="ts">
  // Complete Theme Selector Component - Visual theme picker with live preview
  
  import { onMount } from 'svelte'
  import { Button } from '$lib/components/ui/button'
  import * as Dialog from '$lib/components/ui/dialog'
  import * as Card from '$lib/components/ui/card'
  import { Badge } from '$lib/components/ui/badge'
  import { Palette, Check, Eye, Sparkles, Download, Upload } from 'lucide-svelte'
  import { ThemeManager } from '$lib/themes/theme-manager'
  import { availableThemes, themeCategories } from '$lib/themes/theme-configs'
  import type { ThemeConfig } from '$lib/themes/types'
  
  // Props
  let { 
    size = 'default',
    variant = 'default',
    showBadge = true,
    className = ''
  } = $props()
  
  // State
  let isOpen = $state(false)
  let selectedTheme = $state('')
  let previewTheme = $state<string | null>(null)
  let isLoading = $state(false)
  let error = $state<string | null>(null)
  
  // Theme manager instance
  let themeManager: ThemeManager
  let currentTheme = $state<ThemeConfig | null>(null)
  
  // Initialize theme manager
  onMount(() => {
    themeManager = new ThemeManager(availableThemes, 'default')
    
    // Subscribe to current theme changes
    const unsubscribe = themeManager.currentTheme.subscribe(theme => {
      currentTheme = theme
      selectedTheme = theme?.id || 'default'
    })
    
    // Subscribe to errors
    const unsubscribeError = themeManager.onError(errorMessage => {
      error = errorMessage
      setTimeout(() => error = null, 5000)
    })
    
    return () => {
      unsubscribe()
      unsubscribeError()
      themeManager.destroy()
    }
  })
  
  // Handle theme selection
  function handleThemeSelect(themeId: string) {
    selectedTheme = themeId
  }
  
  // Handle theme preview
  function handlePreview(themeId: string) {
    if (!themeManager) return
    
    previewTheme = themeId
    themeManager.previewTheme(themeId)
  }
  
  // Handle apply theme
  async function handleApply() {
    if (!themeManager || !selectedTheme) return
    
    isLoading = true
    error = null
    
    try {
      await themeManager.switchTheme(selectedTheme)
      previewTheme = null
      isOpen = false
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to apply theme'
    } finally {
      isLoading = false
    }
  }
  
  // Handle cancel
  function handleCancel() {
    if (!themeManager) return
    
    if (previewTheme) {
      themeManager.exitPreview()
      previewTheme = null
    }
    selectedTheme = currentTheme?.id || 'default'
    isOpen = false
    error = null
  }
  
  // Handle export theme
  function handleExport() {
    if (!themeManager) return
    
    try {
      const themeJson = themeManager.exportTheme()
      const blob = new Blob([themeJson], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${currentTheme?.name || 'theme'}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (err) {
      error = 'Failed to export theme'
    }
  }
  
  // Handle import theme
  function handleImport() {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file || !themeManager) return
      
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const themeJson = e.target?.result as string
          themeManager.importTheme(themeJson)
        } catch (err) {
          error = 'Failed to import theme'
        }
      }
      reader.readAsText(file)
    }
    input.click()
  }
  
  // Get theme category info
  function getCategoryInfo(category: string) {
    return themeCategories[category] || { name: category, icon: '🎨' }
  }
  
  // Get theme preview colors
  function getThemeColors(theme: ThemeConfig) {
    return {
      primary: theme.globalStyles.primaryColor,
      secondary: theme.globalStyles.secondaryColor,
      accent: theme.globalStyles.accentColor || theme.globalStyles.primaryColor
    }
  }
</script>

<Dialog.Root bind:open={isOpen}>
  <Dialog.Trigger asChild let:builder>
    <Button 
      builders={[builder]} 
      {variant} 
      {size} 
      class="gap-2 {className}"
    >
      <Palette class="h-4 w-4" />
      <span>Themes</span>
      {#if showBadge && currentTheme}
        <Badge variant="secondary" class="ml-1 text-xs">
          {currentTheme.name}
        </Badge>
      {/if}
    </Button>
  </Dialog.Trigger>
  
  <Dialog.Content class="max-w-7xl max-h-[90vh] overflow-y-auto">
    <Dialog.Header>
      <Dialog.Title class="flex items-center gap-2 text-2xl">
        <Sparkles class="h-6 w-6" />
        Choose Your Store Theme
      </Dialog.Title>
      <Dialog.Description class="text-base">
        Transform your store's appearance instantly. Each theme provides a completely different design and user experience.
      </Dialog.Description>
    </Dialog.Header>
    
    <!-- Error Display -->
    {#if error}
      <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <div class="flex items-center gap-2 text-red-800">
          <span class="font-medium">Error:</span>
          <span>{error}</span>
        </div>
      </div>
    {/if}
    
    <!-- Preview Notice -->
    {#if previewTheme}
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div class="flex items-center gap-2 text-blue-800">
          <Eye class="h-4 w-4" />
          <span class="font-medium">Preview Mode Active</span>
        </div>
        <p class="text-sm text-blue-700 mt-1">
          You're currently previewing the {availableThemes.find(t => t.id === previewTheme)?.name} theme. 
          Click "Apply Theme" to save your selection.
        </p>
      </div>
    {/if}
    
    <!-- Theme Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 py-6">
      {#each availableThemes as theme}
        {@const categoryInfo = getCategoryInfo(theme.category)}
        {@const colors = getThemeColors(theme)}
        {@const isSelected = selectedTheme === theme.id}
        {@const isCurrent = currentTheme?.id === theme.id}
        
        <Card.Root 
          class="cursor-pointer transition-all hover:shadow-lg {isSelected ? 'ring-2 ring-primary shadow-lg' : ''} {isCurrent ? 'border-primary' : ''}"
          onclick={() => handleThemeSelect(theme.id)}
        >
          <Card.Header class="pb-3">
            <div class="flex items-start justify-between">
              <div class="flex items-center gap-3">
                <span class="text-3xl">{categoryInfo.icon}</span>
                <div>
                  <Card.Title class="text-lg flex items-center gap-2">
                    {theme.name}
                    {#if isSelected}
                      <Check class="h-4 w-4 text-primary" />
                    {/if}
                    {#if isCurrent}
                      <Badge variant="default" class="text-xs">Current</Badge>
                    {/if}
                  </Card.Title>
                  <Badge variant="outline" class="text-xs mt-1">
                    {categoryInfo.name}
                  </Badge>
                </div>
              </div>
            </div>
            <Card.Description class="text-sm mt-2">
              {theme.description}
            </Card.Description>
          </Card.Header>
          
          <Card.Content class="space-y-4">
            <!-- Theme Preview -->
            <div class="space-y-3">
              <div class="text-xs font-medium text-muted-foreground">Design Preview</div>
              
              <!-- Color Palette -->
              <div class="flex gap-2">
                <div 
                  class="w-8 h-8 rounded-full border-2 border-white shadow-sm"
                  style="background-color: {colors.primary}"
                  title="Primary Color"
                ></div>
                <div 
                  class="w-8 h-8 rounded-full border-2 border-white shadow-sm"
                  style="background-color: {colors.secondary}"
                  title="Secondary Color"
                ></div>
                <div 
                  class="w-8 h-8 rounded-full border-2 border-white shadow-sm"
                  style="background-color: {colors.accent}"
                  title="Accent Color"
                ></div>
              </div>
              
              <!-- Typography Preview -->
              <div class="text-xs text-muted-foreground">
                <div style="font-family: {theme.globalStyles.fontFamily}">
                  Font: {theme.globalStyles.fontFamily.split(',')[0]}
                </div>
              </div>
              
              <!-- Mini Layout Preview -->
              <div class="border rounded-md p-3 bg-background space-y-2">
                <!-- Header -->
                <div 
                  class="h-3 rounded"
                  style="background-color: {colors.primary}; border-radius: {theme.globalStyles.borderRadius}"
                ></div>
                <!-- Content Grid -->
                <div class="grid grid-cols-3 gap-1">
                  <div 
                    class="h-8 rounded bg-muted"
                    style="border-radius: {theme.globalStyles.borderRadius}"
                  ></div>
                  <div 
                    class="h-8 rounded bg-muted"
                    style="border-radius: {theme.globalStyles.borderRadius}"
                  ></div>
                  <div 
                    class="h-8 rounded bg-muted"
                    style="border-radius: {theme.globalStyles.borderRadius}"
                  ></div>
                </div>
                <!-- Footer -->
                <div 
                  class="h-2 rounded bg-muted"
                  style="border-radius: {theme.globalStyles.borderRadius}"
                ></div>
              </div>
            </div>
            
            <!-- Theme Features -->
            <div class="space-y-2">
              <div class="text-xs font-medium text-muted-foreground">Sections Included</div>
              <div class="flex flex-wrap gap-1">
                {#each theme.sections.filter(s => s.visible) as section}
                  <Badge variant="outline" class="text-xs">
                    {section.component}
                  </Badge>
                {/each}
              </div>
            </div>
            
            <!-- Theme Info -->
            <div class="space-y-1 text-xs text-muted-foreground">
              {#if theme.author}
                <div>By: {theme.author}</div>
              {/if}
              {#if theme.version}
                <div>Version: {theme.version}</div>
              {/if}
            </div>
            
            <!-- Preview Button -->
            <Button 
              variant="outline" 
              size="sm" 
              class="w-full gap-2"
              onclick={(e) => {
                e.stopPropagation()
                handlePreview(theme.id)
              }}
            >
              <Eye class="h-3 w-3" />
              Preview
            </Button>
          </Card.Content>
        </Card.Root>
      {/each}
    </div>
    
    <Dialog.Footer class="gap-2 pt-6 border-t">
      <div class="flex items-center gap-2 mr-auto">
        <Button 
          variant="outline" 
          size="sm"
          onclick={handleExport}
          disabled={!currentTheme}
        >
          <Download class="h-4 w-4 mr-2" />
          Export
        </Button>
        <Button
          variant="outline"
          size="sm"
          onclick={handleImport}
        >
          <Upload class="h-4 w-4 mr-2" />
          Import
        </Button>
      </div>
      
      <Button variant="outline" onclick={handleCancel}>
        Cancel
      </Button>
      <Button 
        onclick={handleApply} 
        disabled={!selectedTheme || isLoading}
      >
        {#if isLoading}
          <div class="flex items-center gap-2">
            <div class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
            <span>Applying...</span>
          </div>
        {:else}
          Apply Theme
        {/if}
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>

<style>
  /* Additional styling for theme preview */
  :global(.theme-selector-dialog) {
    max-height: 90vh;
  }
  
  /* Smooth transitions for theme previews */
  .theme-preview {
    transition: all 0.3s ease;
  }
  
  .theme-preview:hover {
    transform: translateY(-2px);
  }
</style>
