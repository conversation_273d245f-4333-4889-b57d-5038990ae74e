<script lang="ts">
	// Enhanced Homepage with Complete Theme System Integration

	import { onMount } from 'svelte'
	import { page as pageStore } from '$app/stores'
	import { X } from 'lucide-svelte'
	import { fly } from 'svelte/transition'
	import GoogleStructuredDataProductsList from '$lib/core/components/plugins/google-structured-data-products-list.svelte'
	import SeoHeader from '$lib/core/components/plugins/seo-header.svelte'
	import { HomepageModule } from '$lib/core/composables/use-homepage.svelte'
	import { timestampToAgo } from '$lib/core/utils/index.js'

	// Theme System Imports
	import ThemedPage from './themed-page.svelte'
	import ThemeSelector from '$lib/components/theme/simple-theme-selector.svelte'
	import { getGlobalThemeManager } from '$lib/themes'

	// Fallback imports (for when theme system is disabled)
	import FeaturedProductsGrid from '$lib/components/product-catalogue/featured-products-grid.svelte'
	import { Skeleton } from '$lib/components/ui/skeleton'
	import CategoryList from '$lib/components/category/category-list.svelte'
	import HomepageCategoryListWithImage from '$lib/components/home/<USER>'
	import HomepageBanners from '$lib/components/home/<USER>'
	import Banners from '$lib/components/home/<USER>'
	import Collections from '$lib/components/home/<USER>'

	let { data } = $props()

	// Type definitions
	interface ExtendedPage {
		metaTitle?: string
		metaDescription?: string
		metaKeywords?: string
		logo?: string
		desktopBanners?: any[]
		mobileBanners?: any[]
		sections?: any[]
	}

	interface PageData {
		page?: ExtendedPage
		store?: any
		storeId?: string
	}

	// State
	let useThemeSystem = $state(true)
	let themeSystemError = $state<string | null>(null)
	let isThemeSystemReady = $state(false)

	// Cast data.page to ExtendedPage type for TypeScript
	const page = (data?.page || {}) as ExtendedPage
	const homepageModule = new HomepageModule()

	// Initialize theme system
	onMount(async () => {
		try {
			// Check if theme system should be enabled
			const urlParams = new URLSearchParams(window.location.search)
			const disableThemes = urlParams.get('disable-themes') === 'true'

			if (disableThemes) {
				useThemeSystem = false
				isThemeSystemReady = true
				return
			}

			// Initialize theme system
			const themeManager = getGlobalThemeManager()

			// Wait for theme system to be ready
			await new Promise(resolve => setTimeout(resolve, 100))

			isThemeSystemReady = true

		} catch (error) {
			console.error('Failed to initialize theme system:', error)
			themeSystemError = error instanceof Error ? error.message : 'Theme system initialization failed'
			useThemeSystem = false
			isThemeSystemReady = true
		}
	})

	// Handle theme system toggle
	function toggleThemeSystem() {
		useThemeSystem = !useThemeSystem

		// Update URL to reflect theme system state
		const url = new URL(window.location.href)
		if (!useThemeSystem) {
			url.searchParams.set('disable-themes', 'true')
		} else {
			url.searchParams.delete('disable-themes')
		}
		window.history.replaceState({}, '', url.toString())

		// Reload page to apply changes
		window.location.reload()
	}
</script>

<!-- SEO and Structured Data -->
<GoogleStructuredDataProductsList products={homepageModule.featuredProductsStructuredData} />

<SeoHeader
	metaTitle={page?.metaTitle || data?.store?.name || 'Svelte Commerce'}
	metaDescription={page?.metaDescription}
	metaKeywords={page?.metaKeywords}
	image={page?.logo}
/>

<!-- Theme System Status (Development Only) -->
{#if import.meta.env.DEV}
	<div class="fixed top-4 left-4 z-50 flex gap-2">
		<ThemeSelector size="sm" />
		<button
			class="px-3 py-1 text-xs bg-gray-800 text-white rounded hover:bg-gray-700 transition-colors"
			onclick={toggleThemeSystem}
		>
			{useThemeSystem ? 'Disable Themes' : 'Enable Themes'}
		</button>
	</div>
{/if}

<!-- Theme System Error Display -->
{#if themeSystemError}
	<div class="bg-red-50 border border-red-200 p-4 m-4 rounded-lg">
		<div class="flex items-center gap-2 text-red-800">
			<span class="font-medium">Theme System Error:</span>
			<span>{themeSystemError}</span>
		</div>
		<button
			class="mt-2 text-sm text-red-600 underline hover:text-red-800"
			onclick={() => useThemeSystem = false}
		>
			Continue with default layout
		</button>
	</div>
{/if}

<!-- Main Content -->
{#if !isThemeSystemReady}
	<!-- Loading state -->
	<div class="min-h-screen flex items-center justify-center">
		<div class="text-center">
			<div class="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
			<p class="text-gray-600">Loading theme system...</p>
		</div>
	</div>
{:else if useThemeSystem}
	<!-- Themed Homepage -->
	<ThemedPage {data} />
{:else}
	<!-- Fallback to Original Homepage -->
	<div class="original-homepage">
		<!-- Mobile Category List -->
		{#if homepageModule.featuredCategories?.length > 0}
			<div class="mx-2 flex justify-center bg-gray-100 px-2 lg:container lg:mx-auto lg:hidden">
				<CategoryList categories={homepageModule.featuredCategories} />
			</div>
		{/if}

		<!-- Original Homepage Content -->
		<div class="relative w-full">
			{#if homepageModule.loading || !page?.desktopBanners}
				<div class="relative aspect-[16/6] max-h-[50vh] w-full">
					<Skeleton class="h-full w-full rounded-none" />
				</div>
			{:else if page?.desktopBanners?.[0]?.url || page?.mobileBanners?.[0]?.url}
				<Banners sliderBannersDesktop={page?.desktopBanners} sliderBannersMobile={page?.mobileBanners} />
			{/if}
		</div>

		<Collections />

		<div class="mx-2 mb-12 lg:container lg:mx-auto">
			<HomepageCategoryListWithImage categories={homepageModule.featuredCategories} loading={homepageModule.loading} />
		</div>

		{#if page?.sections?.length && page?.sections[0]?.isActive}
			<div class="mx-2 mb-12 xl:mx-24">
				<HomepageBanners bannersList={page?.sections} />
			</div>
		{/if}

		<div class="mx-2 lg:container lg:mx-auto">
			<div class="">
				<div class="mx-auto mb-2 flex items-center justify-center">
					<div class="flex-1 border-2 border-black dark:border-white"></div>
					<div class="mx-4 text-[20px] font-bold tracking-wide">NEW ARRIVALS</div>
					<div class="flex-1 border-2 border-black dark:border-white"></div>
				</div>
				<div class="mx-auto mb-5 flex items-center justify-center text-[12px]">
					<a href="/products" class="mx-auto block h-fit border-b border-black pb-[2px] dark:border-white">View All</a>
				</div>
			</div>
		</div>

		{#if homepageModule.loadingFeaturedProducts}
			<div class="grid grid-cols-1 gap-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:mx-24">
				{#each Array(8) as _}
					<div class="mx-2 space-y-3 lg:container lg:mx-auto">
						<Skeleton class="h-[200px] w-full" />
						<Skeleton class="h-4 w-[250px]" />
						<Skeleton class="h-4 w-[200px]" />
						<Skeleton class="h-4 w-[150px]" />
					</div>
				{/each}
			</div>
		{:else}
			<div class="lg:container lg:mx-auto">
				<FeaturedProductsGrid
					data={homepageModule.featuredProducts}
					displayProduct={homepageModule.showProduct}
					loadMore={homepageModule.loadMoreFeaturedProducts}
				/>
				{#if homepageModule.hasMoreFeaturedProducts}
					<div class="mt-4 flex justify-center">
						<button
							class="mb-2 me-2 rounded-lg border border-gray-800 px-5 py-2.5 text-center text-sm font-medium text-gray-900 hover:bg-gray-900 hover:text-white focus:outline-none focus:ring-4 focus:ring-gray-300 dark:border-gray-600 dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white dark:focus:ring-gray-800"
							onclick={homepageModule.loadMoreFeaturedProducts}
						>
							Load More
						</button>
					</div>
				{/if}
			</div>
		{/if}
	</div>
{/if}

<!-- Recent Order Notification (Global) -->
{#if homepageModule.showRecentOrderPopup}
	<div transition:fly={{ x: 50, duration: 150 }} class="fixed bottom-20 right-4 z-50">
		<div class="relative flex justify-between rounded-lg bg-white p-4 shadow-lg">
			<a href="/products/{homepageModule.selectedRecentOrder?.slug || ''}" class="flex items-center justify-between">
				<div class="flex space-x-2">
					<div class="">
						<img
							src={homepageModule.selectedRecentOrder?.image ||
								homepageModule.selectedRecentOrder?.img ||
								homepageModule.selectedRecentOrder?.thumbnail}
							alt="Product"
							class="h-16 w-16 object-contain sm:h-28 sm:w-28"
						/>
					</div>
					<div class="flex flex-col">
						<p class="text-xs text-black sm:text-sm">
							{homepageModule.selectedRecentOrder?.first_name || 'Someone'} from {homepageModule.selectedRecentOrder?.city || 'somewhere'}
						</p>
						<p class="text-xs text-gray-500 sm:text-sm">purchased a</p>
						<p class="line-clamp-3 max-w-[200px] text-sm text-blue-600">
							{homepageModule.selectedRecentOrder?.title || 'product'}
						</p>
						<p class="mt-auto text-xs text-gray-500 sm:text-xs">
							{timestampToAgo(homepageModule.selectedRecentOrder?.created_at || homepageModule.selectedRecentOrder?.createdAt || '')}
						</p>
					</div>
				</div>
			</a>
			<button class="self-start text-gray-500 hover:text-red-500" onclick={() => (homepageModule.showRecentOrderPopup = false)}>
				<X class="h-4 w-4" />
			</button>
		</div>
	</div>
{/if}

<style>
	/* Original homepage styles */
	.original-homepage {
		min-height: 100vh;
	}

	/* Theme system loading styles */
	.theme-loading {
		display: flex;
		align-items: center;
		justify-content: center;
		min-height: 50vh;
	}

	/* Development controls */
	.dev-controls {
		position: fixed;
		top: 1rem;
		left: 1rem;
		z-index: 1000;
		display: flex;
		gap: 0.5rem;
	}

	/* Responsive adjustments */
	@media (max-width: 768px) {
		.dev-controls {
			top: 0.5rem;
			left: 0.5rem;
			flex-direction: column;
		}
	}
</style>

