<script lang="ts">
  // Enhanced Collections Component - Collections with theme variants
  
  import Collections from './collections.svelte'
  import Product from '$lib/components/product-catalogue/product-card.svelte'
  import { Carousel, CarouselContent, CarouselItem } from '$lib/components/ui/carousel/index.js'
  import CarouselPrevious from '$lib/components/ui/carousel/carousel-previous.svelte'
  import CarouselNext from '$lib/components/ui/carousel/carousel-next.svelte'
  import CollectionsRenderer from '$lib/core/composables/collections-renderer.svelte'
  import { Button } from '$lib/components/ui/button'
  import { ArrowRight, Star, Heart, Eye } from 'lucide-svelte'
  
  let { 
    variant = 'default',
    theme = 'default',
    collections = [],
    showDescription = true,
    itemsPerRow = 4,
    layout = 'carousel',
    ...props 
  } = $props()
  
  // Variant configurations
  const variants = {
    luxury: {
      containerClass: 'luxury-collections py-20 bg-gradient-to-b from-white to-amber-50 relative',
      sectionClass: 'flex flex-col items-center justify-center gap-10 px-8 py-16 max-w-7xl mx-auto',
      titleClass: 'text-center text-5xl font-serif font-bold text-gray-900 mb-4 tracking-wide',
      subtitleClass: 'text-center text-xl font-serif text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed',
      dividerClass: 'w-32 border-2 border-amber-400 mb-8',
      carouselClass: 'w-full',
      itemBasis: 'basis-full mobiles:basis-[45%] tablet:basis-[30%] laptopl:basis-1/4',
      showDescription: true,
      showItemCount: true,
      showViewAll: true,
      cardVariant: 'luxury',
      layout: 'carousel'
    },
    minimal: {
      containerClass: 'minimal-collections py-12 bg-white',
      sectionClass: 'flex flex-col items-center justify-center gap-6 px-4 py-8 max-w-6xl mx-auto',
      titleClass: 'text-center text-2xl font-sans font-light text-gray-900 mb-2',
      subtitleClass: 'text-center text-sm font-sans text-gray-500 mb-6',
      dividerClass: 'w-16 border border-gray-300 mb-6',
      carouselClass: 'w-full',
      itemBasis: 'basis-full mobiles:basis-[50%] tablet:basis-[33%] laptopl:basis-1/4',
      showDescription: false,
      showItemCount: false,
      showViewAll: false,
      cardVariant: 'minimal',
      layout: 'grid'
    },
    fashion: {
      containerClass: 'fashion-collections py-24 bg-gradient-to-br from-pink-50 via-white to-purple-50 relative overflow-hidden',
      sectionClass: 'flex flex-col items-center justify-center gap-12 px-8 py-20 max-w-7xl mx-auto',
      titleClass: 'text-center text-6xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent mb-6 leading-tight',
      subtitleClass: 'text-center text-2xl font-medium text-gray-700 mb-10 max-w-3xl mx-auto',
      dividerClass: 'w-24 h-1 bg-gradient-to-r from-pink-500 to-purple-500 mb-10 rounded-full',
      carouselClass: 'w-full',
      itemBasis: 'basis-full mobiles:basis-[48%] tablet:basis-[32%] laptopl:basis-1/3',
      showDescription: true,
      showItemCount: true,
      showViewAll: true,
      cardVariant: 'fashion',
      layout: 'carousel'
    },
    tech: {
      containerClass: 'tech-collections py-16 bg-slate-50 relative',
      sectionClass: 'flex flex-col items-center justify-center gap-8 px-6 py-12 max-w-6xl mx-auto',
      titleClass: 'text-center text-4xl font-sans font-bold text-slate-900 mb-4',
      subtitleClass: 'text-center text-lg font-sans text-slate-600 mb-8',
      dividerClass: 'w-20 border-2 border-blue-500 mb-8',
      carouselClass: 'w-full',
      itemBasis: 'basis-full mobiles:basis-[50%] tablet:basis-[33%] laptopl:basis-1/4',
      showDescription: true,
      showItemCount: true,
      showViewAll: true,
      cardVariant: 'tech',
      layout: 'grid'
    }
  }
  
  const config = variants[variant] || {
    containerClass: 'py-12',
    sectionClass: 'px-4 py-8',
    titleClass: 'text-2xl font-bold',
    layout: 'carousel'
  }
  
  // Override config with props
  if (showDescription !== undefined) config.showDescription = showDescription
  if (layout !== 'carousel') config.layout = layout
</script>

{#if variant === 'default'}
  <!-- Use original component -->
  <Collections {...props} />
{:else}
  <!-- Enhanced variant -->
  <div class="{config.containerClass}">
    <!-- Background decorations for fashion theme -->
    {#if variant === 'fashion'}
      <div class="absolute top-10 right-10 w-32 h-32 rounded-full bg-gradient-to-r from-pink-400/20 to-purple-500/20 animate-pulse"></div>
      <div class="absolute bottom-20 left-10 w-24 h-24 rounded-full bg-gradient-to-r from-purple-400/20 to-pink-500/20 animate-bounce"></div>
    {/if}
    
    <!-- Tech theme grid background -->
    {#if variant === 'tech'}
      <div class="absolute inset-0 opacity-5">
        <div class="grid grid-cols-12 gap-4 h-full">
          {#each Array(48) as _, i}
            <div class="bg-blue-500 rounded"></div>
          {/each}
        </div>
      </div>
    {/if}
    
    <CollectionsRenderer>
      {#snippet content({ displayProduct, collectionData })}
        {#if collectionData.length > 0}
          {#each collectionData as data, index}
            <div class="{config.sectionClass}">
              <!-- Collection Header -->
              <div class="text-center relative z-10">
                <h2 class="{config.titleClass}">
                  {data.name}
                </h2>
                
                {#if config.showDescription && data.description}
                  <p class="{config.subtitleClass}">
                    {data.description}
                  </p>
                {/if}
                
                {#if config.showItemCount && data.collectionvalues?.length}
                  <div class="flex items-center justify-center gap-2 mb-4">
                    <Star class="w-4 h-4 text-yellow-500" />
                    <span class="text-sm text-gray-500">
                      {data.collectionvalues.length} {data.collectionvalues.length === 1 ? 'item' : 'items'}
                    </span>
                  </div>
                {/if}
                
                <div class="{config.dividerClass}"></div>
              </div>
              
              <!-- Collection Content -->
              {#if config.layout === 'carousel'}
                <!-- Carousel Layout -->
                <Carousel
                  opts={{
                    align: 'start',
                    loop: data.collectionvalues?.length > 4,
                    skipSnaps: false
                  }}
                  class="{config.carouselClass}"
                >
                  <CarouselContent class="-ml-4">
                    {#each data?.collectionvalues as prod (prod?.id)}
                      {#if prod?.products}
                        <CarouselItem class="{config.itemBasis} pl-4">
                          <div class="h-full">
                            <Product
                              product={{
                                id: prod?.products.id,
                                slug: prod?.products.slug,
                                thumbnail: prod?.products.thumbnail,
                                price: prod?.products.price,
                                mrp: prod?.products.mrp,
                                title: prod?.products.title,
                                vendor: prod?.products.vendor,
                                tag: prod?.products.tag,
                                variants: prod?.products.variants
                              }}
                              aspectRatio="square"
                              {displayProduct}
                              variant={config.cardVariant}
                              theme={theme}
                            />
                          </div>
                        </CarouselItem>
                      {/if}
                    {/each}
                  </CarouselContent>

                  <!-- Navigation arrows -->
                  {#if data.collectionvalues?.length > 4}
                    <div class="absolute left-2 top-1/2 z-30 hidden -translate-y-1/2 translate-x-4 transform laptop:flex">
                      <CarouselPrevious class="rounded-full bg-white p-3 text-black shadow-lg hover:bg-gray-100 transition-all duration-200" />
                    </div>

                    <div class="absolute right-2 top-1/2 z-30 hidden -translate-x-4 -translate-y-1/2 transform laptop:flex">
                      <CarouselNext class="rounded-full bg-white p-3 text-black shadow-lg hover:bg-gray-100 transition-all duration-200" />
                    </div>
                  {/if}
                </Carousel>
              {:else}
                <!-- Grid Layout -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 w-full">
                  {#each data?.collectionvalues?.slice(0, 8) as prod (prod?.id)}
                    {#if prod?.products}
                      <div class="h-full">
                        <Product
                          product={{
                            id: prod?.products.id,
                            slug: prod?.products.slug,
                            thumbnail: prod?.products.thumbnail,
                            price: prod?.products.price,
                            mrp: prod?.products.mrp,
                            title: prod?.products.title,
                            vendor: prod?.products.vendor,
                            tag: prod?.products.tag,
                            variants: prod?.products.variants
                          }}
                          aspectRatio="square"
                          {displayProduct}
                          variant={config.cardVariant}
                          theme={theme}
                        />
                      </div>
                    {/if}
                  {/each}
                </div>
              {/if}
              
              <!-- View All Button -->
              {#if config.showViewAll && data.slug}
                <div class="mt-10">
                  <Button 
                    href="/collections/{data.slug}"
                    class="{
                      variant === 'luxury' ? 'bg-gradient-to-r from-amber-400 to-amber-600 text-black hover:from-amber-500 hover:to-amber-700 px-8 py-3 font-bold uppercase tracking-wider' :
                      variant === 'fashion' ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white hover:from-pink-600 hover:to-purple-700 px-8 py-3 rounded-full font-bold' :
                      variant === 'tech' ? 'bg-blue-600 text-white hover:bg-blue-700 px-6 py-3 font-semibold' :
                      'bg-gray-900 text-white hover:bg-gray-800 px-6 py-3'
                    }"
                  >
                    <span>View All {data.name}</span>
                    <ArrowRight class="ml-2 w-4 h-4" />
                  </Button>
                </div>
              {/if}
              
              <!-- Collection Stats for luxury theme -->
              {#if variant === 'luxury' && data.collectionvalues?.length}
                <div class="mt-8 flex items-center justify-center gap-8 text-sm text-gray-600">
                  <div class="flex items-center gap-2">
                    <Eye class="w-4 h-4" />
                    <span>Curated Selection</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <Heart class="w-4 h-4" />
                    <span>Premium Quality</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <Star class="w-4 h-4" />
                    <span>Exclusive Items</span>
                  </div>
                </div>
              {/if}
            </div>
          {/each}
        {:else}
          <!-- Empty state -->
          <div class="flex flex-col items-center justify-center py-20">
            <div class="text-center max-w-md">
              <div class="text-6xl mb-6">
                {#if variant === 'luxury'}
                  💎
                {:else if variant === 'fashion'}
                  👗
                {:else if variant === 'tech'}
                  💻
                {:else}
                  📦
                {/if}
              </div>
              <h3 class="text-2xl font-bold text-gray-900 mb-4">No collections found</h3>
              <p class="text-gray-600 mb-6">
                {#if variant === 'luxury'}
                  Create curated collections to showcase your premium products
                {:else if variant === 'fashion'}
                  Build trendy collections to highlight your latest styles
                {:else if variant === 'tech'}
                  Organize your products into technical categories
                {:else}
                  Get started by creating your first product collection
                {/if}
              </p>
              <Button 
                href="/admin/collections"
                class="{
                  variant === 'luxury' ? 'bg-gradient-to-r from-amber-400 to-amber-600 text-black' :
                  variant === 'fashion' ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white' :
                  variant === 'tech' ? 'bg-blue-600 text-white' :
                  'bg-gray-900 text-white'
                }"
              >
                Create Collection
              </Button>
            </div>
          </div>
        {/if}
      {/snippet}
    </CollectionsRenderer>
  </div>
{/if}

<style>
  /* Luxury theme styles */
  .luxury-collections {
    position: relative;
  }
  
  .luxury-collections::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #d4af37, transparent);
  }
  
  .luxury-collections::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #d4af37, transparent);
  }
  
  /* Fashion theme styles */
  .fashion-collections {
    position: relative;
    overflow: hidden;
  }
  
  .fashion-collections::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(236, 72, 153, 0.05) 0%, transparent 70%);
    animation: float 25s ease-in-out infinite;
    z-index: 0;
  }
  
  /* Tech theme styles */
  .tech-collections {
    position: relative;
  }
  
  .tech-collections::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #1e40af, #3b82f6);
  }
  
  /* Animations */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-30px) rotate(180deg);
    }
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .luxury-collections,
    .minimal-collections,
    .fashion-collections,
    .tech-collections {
      padding: 2rem 0;
    }
    
    .luxury-collections .text-5xl {
      font-size: 2.5rem;
    }
    
    .fashion-collections .text-6xl {
      font-size: 3rem;
    }
  }
  
  /* Hover effects */
  .luxury-collections:hover {
    background: linear-gradient(to bottom, #ffffff 0%, #fefbf3 50%, #fef7e0 100%);
  }
  
  .fashion-collections:hover .bg-gradient-to-r {
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }
  
  @keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }
</style>
