<script lang="ts">
  // Simple Theme Selector - No complex dependencies
  
  import { onMount } from 'svelte'
  import { Button } from '$lib/components/ui/button'
  import * as Dialog from '$lib/components/ui/dialog'
  import { Badge } from '$lib/components/ui/badge'
  import { ThemeManager } from '$lib/themes/theme-manager'
  import { availableThemes } from '$lib/themes/theme-configs'
  import type { ThemeConfig } from '$lib/themes/types'
  
  // Props
  let { 
    size = 'default',
    variant = 'default',
    showBadge = true,
    className = ''
  } = $props()
  
  // State
  let isOpen = $state(false)
  let selectedTheme = $state('')
  let previewTheme = $state<string | null>(null)
  let isLoading = $state(false)
  let error = $state<string | null>(null)
  
  // Theme manager instance
  let themeManager: ThemeManager
  let currentTheme = $state<ThemeConfig | null>(null)
  
  // Initialize theme manager
  onMount(() => {
    themeManager = new ThemeManager(availableThemes, 'default')
    
    // Subscribe to current theme changes
    const unsubscribe = themeManager.currentTheme.subscribe(theme => {
      currentTheme = theme
      selectedTheme = theme?.id || 'default'
    })
    
    // Subscribe to errors
    const unsubscribeError = themeManager.onError(errorMessage => {
      error = errorMessage
      setTimeout(() => error = null, 5000)
    })
    
    return () => {
      unsubscribe()
      unsubscribeError()
      themeManager.destroy()
    }
  })
  
  // Handle theme selection
  function handleThemeSelect(themeId: string) {
    selectedTheme = themeId
  }
  
  // Handle theme preview
  function handlePreview(themeId: string) {
    if (!themeManager) return
    
    previewTheme = themeId
    themeManager.previewTheme(themeId)
  }
  
  // Handle apply theme
  async function handleApply() {
    if (!themeManager || !selectedTheme) return
    
    isLoading = true
    error = null
    
    try {
      await themeManager.switchTheme(selectedTheme)
      previewTheme = null
      isOpen = false
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to apply theme'
    } finally {
      isLoading = false
    }
  }
  
  // Handle cancel
  function handleCancel() {
    if (!themeManager) return
    
    if (previewTheme) {
      themeManager.exitPreview()
      previewTheme = null
    }
    selectedTheme = currentTheme?.id || 'default'
    isOpen = false
    error = null
  }
  
  // Get theme preview colors
  function getThemeColors(theme: ThemeConfig) {
    return {
      primary: theme.globalStyles.primaryColor,
      secondary: theme.globalStyles.secondaryColor,
      accent: theme.globalStyles.accentColor || theme.globalStyles.primaryColor
    }
  }
</script>

<Dialog.Root bind:open={isOpen}>
  <Dialog.Trigger asChild let:builder>
    <Button 
      builders={[builder]} 
      {variant} 
      {size} 
      class="gap-2 {className}"
    >
      <span>🎨</span>
      <span>Themes</span>
      {#if showBadge && currentTheme}
        <Badge variant="secondary" class="ml-1 text-xs">
          {currentTheme.name}
        </Badge>
      {/if}
    </Button>
  </Dialog.Trigger>
  
  <Dialog.Content class="max-w-4xl max-h-[90vh] overflow-y-auto">
    <Dialog.Header>
      <Dialog.Title class="flex items-center gap-2 text-xl">
        <span>✨</span>
        Choose Your Store Theme
      </Dialog.Title>
      <Dialog.Description>
        Transform your store's appearance instantly. Each theme provides a completely different design.
      </Dialog.Description>
    </Dialog.Header>
    
    <!-- Error Display -->
    {#if error}
      <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
        <div class="flex items-center gap-2 text-red-800">
          <span>⚠️</span>
          <span class="font-medium">Error:</span>
          <span>{error}</span>
        </div>
      </div>
    {/if}
    
    <!-- Preview Notice -->
    {#if previewTheme}
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
        <div class="flex items-center gap-2 text-blue-800">
          <span>👁️</span>
          <span class="font-medium">Preview Mode Active</span>
        </div>
        <p class="text-sm text-blue-700 mt-1">
          You're currently previewing the {availableThemes.find(t => t.id === previewTheme)?.name} theme. 
          Click "Apply Theme" to save your selection.
        </p>
      </div>
    {/if}
    
    <!-- Theme Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 py-4">
      {#each availableThemes as theme}
        {@const colors = getThemeColors(theme)}
        {@const isSelected = selectedTheme === theme.id}
        {@const isCurrent = currentTheme?.id === theme.id}
        
        <div 
          class="border rounded-lg p-4 cursor-pointer transition-all hover:shadow-md {isSelected ? 'ring-2 ring-blue-500 shadow-md' : ''} {isCurrent ? 'border-blue-500' : 'border-gray-200'}"
          onclick={() => handleThemeSelect(theme.id)}
        >
          <div class="flex items-start justify-between mb-3">
            <div>
              <h3 class="font-semibold flex items-center gap-2">
                {theme.name}
                {#if isSelected}
                  <span class="text-blue-500">✓</span>
                {/if}
                {#if isCurrent}
                  <Badge variant="default" class="text-xs">Current</Badge>
                {/if}
              </h3>
              <Badge variant="outline" class="text-xs mt-1">
                {theme.category}
              </Badge>
            </div>
          </div>
          
          <p class="text-sm text-gray-600 mb-3">
            {theme.description}
          </p>
          
          <!-- Color Preview -->
          <div class="flex gap-2 mb-3">
            <div 
              class="w-6 h-6 rounded-full border-2 border-white shadow-sm"
              style="background-color: {colors.primary}"
              title="Primary Color"
            ></div>
            <div 
              class="w-6 h-6 rounded-full border-2 border-white shadow-sm"
              style="background-color: {colors.secondary}"
              title="Secondary Color"
            ></div>
            <div 
              class="w-6 h-6 rounded-full border-2 border-white shadow-sm"
              style="background-color: {colors.accent}"
              title="Accent Color"
            ></div>
          </div>
          
          <!-- Font Preview -->
          <div class="text-xs text-gray-500 mb-3">
            <div style="font-family: {theme.globalStyles.fontFamily}">
              Font: {theme.globalStyles.fontFamily.split(',')[0]}
            </div>
          </div>
          
          <!-- Mini Layout Preview -->
          <div class="border rounded p-2 bg-gray-50 mb-3">
            <div 
              class="h-2 rounded mb-1"
              style="background-color: {colors.primary}; border-radius: {theme.globalStyles.borderRadius}"
            ></div>
            <div class="grid grid-cols-3 gap-1 mb-1">
              <div 
                class="h-6 rounded bg-gray-200"
                style="border-radius: {theme.globalStyles.borderRadius}"
              ></div>
              <div 
                class="h-6 rounded bg-gray-200"
                style="border-radius: {theme.globalStyles.borderRadius}"
              ></div>
              <div 
                class="h-6 rounded bg-gray-200"
                style="border-radius: {theme.globalStyles.borderRadius}"
              ></div>
            </div>
            <div 
              class="h-1 rounded bg-gray-200"
              style="border-radius: {theme.globalStyles.borderRadius}"
            ></div>
          </div>
          
          <!-- Preview Button -->
          <button 
            class="w-full px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors"
            onclick={(e) => {
              e.stopPropagation()
              handlePreview(theme.id)
            }}
          >
            👁️ Preview
          </button>
        </div>
      {/each}
    </div>
    
    <Dialog.Footer class="gap-2 pt-4 border-t">
      <Button variant="outline" onclick={handleCancel}>
        Cancel
      </Button>
      <Button 
        onclick={handleApply} 
        disabled={!selectedTheme || isLoading}
      >
        {#if isLoading}
          <span>⏳ Applying...</span>
        {:else}
          Apply Theme
        {/if}
      </Button>
    </Dialog.Footer>
  </Dialog.Content>
</Dialog.Root>

<style>
  /* Simple theme selector styles */
  .theme-preview {
    transition: all 0.2s ease;
  }
  
  .theme-preview:hover {
    transform: translateY(-1px);
  }
</style>
