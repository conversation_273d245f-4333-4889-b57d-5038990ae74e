<script lang="ts">
  // Themed Homepage - Complete section-based homepage with theme support
  
  import { onMount, onDestroy } from 'svelte'
  import { page } from '$app/stores'
  import { HomepageModule } from '$lib/core/composables/use-homepage.svelte'
  const homepageModule = new HomepageModule()
  import SectionManager from '$lib/themes/section-manager.svelte'
  import { getGlobalThemeManager, ThemeSystemUtils } from '$lib/themes'
  import type { SectionData, ThemeConfig } from '$lib/themes/types'
  
  // Props
  let { data } = $props()
  
  // State
  let currentTheme = $state<ThemeConfig | null>(null)
  let isLoading = $state(true)
  let error = $state<string | null>(null)
  let themeManager: any = null
  
  // Reactive section data using runes
  let sectionData = $derived(prepareSectionData(data))
  
  // Prepare data for sections
  function prepareSectionData(pageData: any): SectionData {
    return {
      // Page data
      page: pageData.page,
      
      // Homepage module data
      featuredCategories: homepageModule.featuredCategories,
      featuredProducts: homepageModule.featuredProducts,
      collections: homepageModule.collections,
      loading: homepageModule.loading,
      
      // Banner data
      banners: pageData.page?.desktopBanners || pageData.page?.banners || [],
      sliderBannersDesktop: pageData.page?.desktopBanners,
      sliderBannersMobile: pageData.page?.mobileBanners,
      
      // Functions
      displayProduct: homepageModule.showProduct,
      loadMore: homepageModule.loadMoreFeaturedProducts,
      
      // Additional data
      brands: pageData.brands || [],
      testimonials: pageData.testimonials || [],
      
      // Store info
      store: pageData.store,
      
      // SEO data
      seo: pageData.seo
    }
  }
  
  // Initialize theme system
  onMount(async () => {
    try {
      isLoading = true
      error = null
      
      // Get global theme manager
      themeManager = getGlobalThemeManager()
      
      // Subscribe to theme changes
      const unsubscribeTheme = themeManager.currentTheme.subscribe((theme: ThemeConfig) => {
        currentTheme = theme
        
        // Apply theme-specific meta tags and styles
        if (theme) {
          applyThemeMetadata(theme)
        }
      })
      
      // Subscribe to errors
      const unsubscribeError = themeManager.onError((errorMessage: string) => {
        error = errorMessage
        console.error('Theme system error:', errorMessage)
      })
      
      // Apply initial theme
      themeManager.applyGlobalStyles()
      
      // Cleanup function
      return () => {
        unsubscribeTheme()
        unsubscribeError()
      }
      
    } catch (err) {
      error = err instanceof Error ? err.message : 'Failed to initialize theme system'
      console.error('Theme initialization error:', err)
    } finally {
      isLoading = false
    }
  })
  
  // Apply theme-specific metadata
  function applyThemeMetadata(theme: ThemeConfig) {
    if (typeof document === 'undefined') return
    
    // Update page title with theme info
    const baseTitle = data.seo?.title || data.store?.name || 'Store'
    document.title = `${baseTitle} - ${theme.name} Theme`
    
    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]')
    if (metaDescription && data.seo?.description) {
      metaDescription.setAttribute('content', `${data.seo.description} - Powered by ${theme.name} theme`)
    }
    
    // Add theme-specific meta tags
    addOrUpdateMetaTag('theme-name', theme.name)
    addOrUpdateMetaTag('theme-category', theme.category)
    addOrUpdateMetaTag('theme-version', theme.version || '1.0.0')
    
    // Add theme color for mobile browsers
    addOrUpdateMetaTag('theme-color', theme.globalStyles.primaryColor, 'name')
    
    // Update favicon color if supported
    updateFaviconColor(theme.globalStyles.primaryColor)
  }
  
  // Helper function to add or update meta tags
  function addOrUpdateMetaTag(name: string, content: string, attribute: string = 'name') {
    let meta = document.querySelector(`meta[${attribute}="${name}"]`)
    if (!meta) {
      meta = document.createElement('meta')
      meta.setAttribute(attribute, name)
      document.head.appendChild(meta)
    }
    meta.setAttribute('content', content)
  }
  
  // Update favicon color (for browsers that support it)
  function updateFaviconColor(color: string) {
    try {
      // Create a colored favicon dynamically
      const canvas = document.createElement('canvas')
      canvas.width = 32
      canvas.height = 32
      const ctx = canvas.getContext('2d')
      
      if (ctx) {
        // Draw colored circle
        ctx.fillStyle = color
        ctx.beginPath()
        ctx.arc(16, 16, 14, 0, 2 * Math.PI)
        ctx.fill()
        
        // Add store initial or icon
        ctx.fillStyle = '#ffffff'
        ctx.font = 'bold 16px Arial'
        ctx.textAlign = 'center'
        ctx.textBaseline = 'middle'
        ctx.fillText(data.store?.name?.charAt(0) || 'S', 16, 16)
        
        // Update favicon
        const favicon = document.querySelector('link[rel="icon"]') as HTMLLinkElement
        if (favicon) {
          favicon.href = canvas.toDataURL()
        }
      }
    } catch (err) {
      console.warn('Failed to update favicon:', err)
    }
  }
  
  // Get theme sections
  function getThemeSections() {
    if (!currentTheme) return []
    return ThemeSystemUtils.getThemeSections(currentTheme.id)
  }
  
  // Handle theme change from URL params using effect
  $effect(() => {
    const urlTheme = $page.url.searchParams.get('theme')
    if (urlTheme && themeManager && currentTheme?.id !== urlTheme) {
      themeManager.switchTheme(urlTheme).catch((err: Error) => {
        console.error('Failed to switch theme from URL:', err)
      })
    }
  })
  
  // Cleanup on destroy
  onDestroy(() => {
    // Reset document modifications
    if (typeof document !== 'undefined') {
      document.body.className = document.body.className.replace(/theme-\w+/g, '')
    }
  })
</script>

<!-- SEO and Meta Tags -->
<svelte:head>
  {#if currentTheme}
    <meta name="theme-name" content={currentTheme.name} />
    <meta name="theme-category" content={currentTheme.category} />
    <meta name="theme-color" content={currentTheme.globalStyles.primaryColor} />
    
    <!-- Theme-specific CSS variables -->
    <style>
      :root {
        {#each Object.entries(ThemeSystemUtils.getThemeCSSVariables(currentTheme)) as [property, value]}
          {property}: {value};
        {/each}
      }
    </style>
    
    <!-- Theme-specific custom CSS -->
    {#if currentTheme.customCSS}
      <style>
        {currentTheme.customCSS}
      </style>
    {/if}
  {/if}
</svelte:head>

<!-- Main Content - No spacing, seamless integration -->
<div class="themed-homepage {currentTheme ? `theme-${currentTheme.id}` : ''}" data-theme={currentTheme?.id}>
  {#if isLoading}
    <!-- Loading State -->
    <div class="loading-container">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <h2 class="loading-title">Loading your store...</h2>
        <p class="loading-description">Setting up the perfect theme for your business</p>
      </div>
    </div>
  {:else if error}
    <!-- Error State -->
    <div class="error-container">
      <div class="error-content">
        <div class="error-icon">⚠️</div>
        <h2 class="error-title">Theme Loading Error</h2>
        <p class="error-description">{error}</p>
        <button
          class="error-retry"
          onclick={() => window.location.reload()}
        >
          Retry
        </button>
      </div>
    </div>
  {:else if currentTheme}
    <!-- Themed Content - Seamless with header/footer -->
    <SectionManager
      theme={currentTheme.id}
      sections={getThemeSections()}
      data={sectionData}
      className="themed-sections"
    />
  {:else}
    <!-- Fallback Content -->
    <div class="fallback-container">
      <div class="fallback-content">
        <h2 class="fallback-title">Welcome to your store</h2>
        <p class="fallback-description">Your theme is loading...</p>
      </div>
    </div>
  {/if}
</div>

<style>
  .themed-homepage {
    /* Remove min-height to prevent spacing issues */
    position: relative;
    transition: all 0.3s ease;
    /* Ensure no unwanted spacing */
    margin: 0;
    padding: 0;
  }
  
  /* Loading State */
  .loading-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  }
  
  .loading-content {
    text-align: center;
    max-width: 400px;
    padding: 2rem;
  }
  
  .loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 2rem;
  }
  
  .loading-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
  }
  
  .loading-description {
    color: #6b7280;
    font-size: 1rem;
  }
  
  /* Error State */
  .error-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fef2f2;
  }
  
  .error-content {
    text-align: center;
    max-width: 400px;
    padding: 2rem;
  }
  
  .error-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
  }
  
  .error-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #dc2626;
    margin-bottom: 0.5rem;
  }
  
  .error-description {
    color: #7f1d1d;
    margin-bottom: 1.5rem;
  }
  
  .error-retry {
    background: #dc2626;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .error-retry:hover {
    background: #b91c1c;
  }
  
  /* Fallback State */
  .fallback-container {
    min-height: 50vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .fallback-content {
    text-align: center;
    max-width: 400px;
    padding: 2rem;
  }
  
  .fallback-title {
    font-size: 2rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
  }
  
  .fallback-description {
    color: #6b7280;
    font-size: 1.125rem;
  }
  
  /* Clean interface - no attribution clutter */
  
  /* Animations */
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  /* Theme-specific styles */
  .theme-luxury {
    background: linear-gradient(180deg, #ffffff 0%, #fefbf3 100%);
  }
  
  .theme-minimal {
    background: #ffffff;
  }
  
  .theme-fashion {
    background: linear-gradient(135deg, #fdf2f8 0%, #f3e8ff 100%);
  }
  
  .theme-tech {
    background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  }
  
  /* Responsive */
  @media (max-width: 768px) {
    .theme-attribution {
      bottom: 0.5rem;
      right: 0.5rem;
      font-size: 0.625rem;
      padding: 0.375rem 0.75rem;
    }
    
    .loading-content,
    .error-content,
    .fallback-content {
      padding: 1rem;
    }
  }
</style>
