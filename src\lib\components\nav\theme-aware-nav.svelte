<script lang="ts">
  // Theme-Aware Navigation - Renders different header styles based on current theme
  
  import { getGlobalThemeManager } from '$lib/themes'
  import Nav from './nav.svelte'
  import { onMount } from 'svelte'
  
  let { storeData, ...props } = $props()
  
  let currentTheme = $state('default')
  let themeManager: any = null
  
  onMount(() => {
    themeManager = getGlobalThemeManager()
    if (themeManager) {
      // Subscribe to theme changes
      const unsubscribe = themeManager.currentTheme.subscribe((theme: any) => {
        currentTheme = theme?.id || 'default'
      })
      
      return unsubscribe
    }
  })
  
  // Theme-specific header configurations
  const headerVariants = {
    default: {
      component: Nav,
      containerClass: 'shadow-xs sticky top-0 z-50 w-full',
      logoClass: 'h-10 object-contain',
      navClass: 'hidden items-center space-x-6 lg:flex',
      linkClass: 'relative text-sm font-medium text-muted-foreground transition-colors hover:text-primary',
      showMegaMenu: true,
      layout: 'horizontal'
    },
    luxury: {
      component: Nav,
      containerClass: 'border-b border-amber-200 bg-gradient-to-r from-amber-50 to-white sticky top-0 z-50 w-full shadow-lg',
      logoClass: 'h-12 object-contain',
      navClass: 'hidden items-center space-x-8 lg:flex',
      linkClass: 'relative text-sm font-serif font-medium text-amber-800 transition-colors hover:text-amber-600 uppercase tracking-wider',
      showMegaMenu: true,
      layout: 'centered',
      showTagline: true,
      tagline: 'Luxury & Elegance'
    },
    minimal: {
      component: Nav,
      containerClass: 'border-b border-gray-100 bg-white sticky top-0 z-50 w-full',
      logoClass: 'h-8 object-contain',
      navClass: 'hidden items-center space-x-4 lg:flex',
      linkClass: 'text-xs font-light text-gray-700 transition-colors hover:text-black uppercase tracking-widest',
      showMegaMenu: false,
      layout: 'minimal',
      showSearch: false
    },
    fashion: {
      component: Nav,
      containerClass: 'bg-gradient-to-r from-pink-50 via-white to-purple-50 sticky top-0 z-50 w-full shadow-md border-b-2 border-gradient-to-r from-pink-200 to-purple-200',
      logoClass: 'h-11 object-contain',
      navClass: 'hidden items-center space-x-6 lg:flex',
      linkClass: 'relative text-sm font-bold text-gray-800 transition-colors hover:text-pink-600 uppercase',
      showMegaMenu: true,
      layout: 'fashion',
      showSocialIcons: true,
      showPromoBar: true,
      promoText: '✨ New Collection Available ✨'
    },
    tech: {
      component: Nav,
      containerClass: 'bg-slate-900 text-white sticky top-0 z-50 w-full shadow-xl border-b border-blue-500',
      logoClass: 'h-10 object-contain filter brightness-0 invert',
      navClass: 'hidden items-center space-x-6 lg:flex',
      linkClass: 'relative text-sm font-medium text-blue-100 transition-colors hover:text-blue-400 uppercase tracking-wide',
      showMegaMenu: true,
      layout: 'tech',
      showSearchBar: true,
      darkMode: true
    }
  }
  
  $: config = headerVariants[currentTheme] || headerVariants.default
</script>

<!-- Theme-specific header wrapper -->
<div class="theme-header theme-{currentTheme}" data-theme={currentTheme}>
  <!-- Luxury theme tagline -->
  {#if currentTheme === 'luxury' && config.showTagline}
    <div class="bg-gradient-to-r from-amber-100 to-amber-50 py-1 text-center">
      <p class="text-xs font-serif text-amber-700 tracking-widest uppercase">{config.tagline}</p>
    </div>
  {/if}
  
  <!-- Fashion theme promo bar -->
  {#if currentTheme === 'fashion' && config.showPromoBar}
    <div class="bg-gradient-to-r from-pink-500 to-purple-600 py-2 text-center">
      <p class="text-sm font-bold text-white animate-pulse">{config.promoText}</p>
    </div>
  {/if}
  
  <!-- Tech theme notification bar -->
  {#if currentTheme === 'tech'}
    <div class="bg-blue-600 py-1 text-center">
      <p class="text-xs text-blue-100">🚀 Latest Tech • Free Shipping • 24/7 Support</p>
    </div>
  {/if}
  
  <!-- Main header with theme-specific styling -->
  <header class="{config.containerClass} theme-header-{currentTheme}">
    <!-- Use the original Nav component without extra props -->
    <Nav {storeData} />
  </header>
</div>

<style>
  /* Theme-specific header styles */
  .theme-header-luxury {
    background: linear-gradient(135deg, #fefbf3 0%, #f7f3e9 100%);
    border-bottom: 2px solid #d4af37;
  }
  
  .theme-header-minimal {
    background: #ffffff;
    border-bottom: 1px solid #f3f4f6;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  }
  
  .theme-header-fashion {
    background: linear-gradient(135deg, #fdf2f8 0%, #f3e8ff 100%);
    border-bottom: 3px solid transparent;
    border-image: linear-gradient(90deg, #ec4899, #8b5cf6) 1;
  }
  
  .theme-header-tech {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    border-bottom: 2px solid #3b82f6;
    box-shadow: 0 4px 6px -1px rgb(59 130 246 / 0.1);
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .theme-header-luxury .text-xs {
      font-size: 0.625rem;
    }
    
    .theme-header-fashion .animate-pulse {
      animation-duration: 2s;
    }
  }
</style>
