<script lang="ts">
  // Enhanced Mobile Categories Component - Mobile category navigation
  
  import CategoryList from '$lib/components/category/category-list.svelte'
  
  let { 
    variant = 'default',
    theme = 'default',
    categories = [],
    ...props 
  } = $props()
  
  // Variant configurations
  const variants = {
    default: {
      containerClass: 'mx-2 flex justify-center bg-gray-100 px-2 lg:container lg:mx-auto lg:hidden',
      showOnMobile: true
    },
    luxury: {
      containerClass: 'mx-2 flex justify-center bg-gradient-to-r from-amber-50 to-amber-100 px-2 lg:container lg:mx-auto lg:hidden border-t border-b border-amber-200',
      showOnMobile: true
    },
    minimal: {
      containerClass: 'mx-2 flex justify-center bg-white px-2 lg:container lg:mx-auto lg:hidden border-b border-gray-200',
      showOnMobile: true
    },
    fashion: {
      containerClass: 'mx-2 flex justify-center bg-gradient-to-r from-pink-50 to-purple-50 px-2 lg:container lg:mx-auto lg:hidden border-t border-b border-pink-200',
      showOnMobile: true
    },
    tech: {
      containerClass: 'mx-2 flex justify-center bg-gradient-to-r from-blue-50 to-slate-50 px-2 lg:container lg:mx-auto lg:hidden border-t border-b border-blue-200',
      showOnMobile: true
    }
  }
  
  const config = variants[variant] || variants.default
</script>

{#if categories?.length > 0 && config.showOnMobile}
  <div class="{config.containerClass}">
    <CategoryList {categories} {...props} />
  </div>
{/if}

<style>
  /* Mobile categories styles */
  :global(.mobile-categories) {
    /* Ensure proper mobile visibility */
    display: flex;
  }
  
  @media (min-width: 1024px) {
    :global(.mobile-categories) {
      display: none;
    }
  }
  
  /* Theme-specific styles */
  :global(.theme-luxury .mobile-categories) {
    background: linear-gradient(90deg, #fef7e0 0%, #fef3c7 100%);
  }
  
  :global(.theme-minimal .mobile-categories) {
    background: #ffffff;
    border-color: #e5e7eb;
  }
  
  :global(.theme-fashion .mobile-categories) {
    background: linear-gradient(90deg, #fdf2f8 0%, #faf5ff 100%);
  }
  
  :global(.theme-tech .mobile-categories) {
    background: linear-gradient(90deg, #eff6ff 0%, #f8fafc 100%);
  }
</style>
