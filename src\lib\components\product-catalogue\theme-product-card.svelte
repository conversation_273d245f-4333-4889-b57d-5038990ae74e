<script lang="ts">
  // Theme-Aware Product Card - Completely different card designs per theme
  
  import { page } from '$app/state'
  import { Plus, Minus, Star, Heart, Eye, ShoppingCart } from 'lucide-svelte'
  import { Button } from '$lib/components/ui/button'
  import LoadingDots from '$lib/core/components/common/loading-dots.svelte'
  import LazyImg from '$lib/core/components/image/lazy-img.svelte'
  import EmptyImage from '$lib/core/components/image/empty-image.svelte'
  import { getCartState } from '$lib/core/stores/cart.svelte'
  import { formatPrice } from '$lib/core/utils'
  
  let { 
    product, 
    variant = 'default',
    theme = 'default',
    aspectRatio = 'square',
    displayProduct,
    layout = 'grid',
    ...props 
  } = $props()
  
  const cartState = getCartState()
  
  // Theme-specific card configurations
  const cardVariants = {
    default: {
      containerClass: 'group bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300',
      imageClass: 'w-full h-48 object-cover',
      contentClass: 'p-4',
      titleClass: 'text-sm font-medium text-gray-900 line-clamp-2 mb-2',
      priceClass: 'text-lg font-bold text-gray-900',
      buttonClass: 'w-full bg-blue-600 text-white hover:bg-blue-700',
      layout: 'standard'
    },
    luxury: {
      containerClass: 'group bg-gradient-to-b from-white to-amber-50 border-2 border-amber-200 rounded-none overflow-hidden hover:shadow-2xl hover:border-amber-300 transition-all duration-500 transform hover:-translate-y-2',
      imageClass: 'w-full h-64 object-cover transition-transform duration-700 group-hover:scale-105',
      contentClass: 'p-6 text-center',
      titleClass: 'text-lg font-serif font-bold text-gray-900 mb-3 leading-tight',
      priceClass: 'text-2xl font-serif font-bold text-amber-700 mb-4',
      buttonClass: 'bg-gradient-to-r from-amber-400 to-amber-600 text-black font-bold uppercase tracking-widest px-6 py-3 hover:from-amber-500 hover:to-amber-700 transition-all duration-300',
      layout: 'luxury'
    },
    minimal: {
      containerClass: 'group bg-white border-b border-gray-100 hover:bg-gray-50 transition-colors duration-200',
      imageClass: 'w-full h-40 object-cover',
      contentClass: 'p-3',
      titleClass: 'text-sm font-light text-gray-900 mb-2',
      priceClass: 'text-base font-medium text-gray-900',
      buttonClass: 'text-xs bg-black text-white hover:bg-gray-800 px-4 py-2',
      layout: 'minimal'
    },
    fashion: {
      containerClass: 'group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-400 transform hover:-translate-y-1 hover:rotate-1',
      imageClass: 'w-full h-56 object-cover transition-transform duration-500 group-hover:scale-110',
      contentClass: 'p-5 bg-gradient-to-r from-pink-50 to-purple-50',
      titleClass: 'text-base font-bold text-gray-900 mb-3 line-clamp-2',
      priceClass: 'text-xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent mb-4',
      buttonClass: 'w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white font-bold rounded-full py-3 hover:from-pink-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105',
      layout: 'fashion'
    },
    tech: {
      containerClass: 'group bg-slate-900 border border-blue-500 rounded-lg overflow-hidden hover:border-blue-400 hover:shadow-xl hover:shadow-blue-500/20 transition-all duration-300',
      imageClass: 'w-full h-48 object-cover bg-slate-800',
      contentClass: 'p-4 text-white',
      titleClass: 'text-sm font-medium text-blue-100 mb-2 line-clamp-2',
      priceClass: 'text-lg font-bold text-blue-400 mb-3',
      buttonClass: 'w-full bg-blue-600 text-white hover:bg-blue-700 border border-blue-500 font-medium',
      layout: 'tech'
    }
  }
  
  const config = cardVariants[variant] || cardVariants.default
  
  function handleCardClick() {
    if (displayProduct) {
      displayProduct(product)
    }
  }
  
  function addToCart() {
    // Add to cart logic
    console.log('Add to cart:', product)
  }
  
  function toggleWishlist() {
    // Wishlist logic
    console.log('Toggle wishlist:', product)
  }
</script>

<!-- Theme-specific product card -->
<div class="{config.containerClass} theme-card theme-{variant}">
  
  {#if config.layout === 'luxury'}
    <!-- Luxury Card Layout -->
    <div class="relative">
      <!-- Product Image -->
      <div class="relative overflow-hidden">
        {#if product.thumbnail || product?.image_url}
          <LazyImg
            src={product.thumbnail || product?.image_url}
            alt="{product.name} product image"
            class="{config.imageClass}"
          />
        {:else}
          <EmptyImage class="{config.imageClass}" />
        {/if}
        
        <!-- Luxury overlay -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        
        <!-- Premium badge -->
        {#if product.tag}
          <div class="absolute top-4 left-4 bg-amber-500 text-black px-3 py-1 text-xs font-bold uppercase tracking-wider">
            {product.tag.title}
          </div>
        {/if}
        
        <!-- Action buttons -->
        <div class="absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <button class="w-10 h-10 bg-white/90 rounded-full flex items-center justify-center hover:bg-white transition-colors" onclick={toggleWishlist}>
            <Heart class="w-4 h-4 text-amber-600" />
          </button>
          <button class="w-10 h-10 bg-white/90 rounded-full flex items-center justify-center hover:bg-white transition-colors" onclick={handleCardClick}>
            <Eye class="w-4 h-4 text-amber-600" />
          </button>
        </div>
      </div>
      
      <!-- Content -->
      <div class="{config.contentClass}">
        <h3 class="{config.titleClass}">{product.title}</h3>
        <div class="flex items-center justify-center gap-1 mb-3">
          {#each Array(5) as _, i}
            <Star class="w-4 h-4 text-amber-400 fill-current" />
          {/each}
        </div>
        <p class="{config.priceClass}">
          {formatPrice(product.price, page?.data?.store?.currency?.code)}
        </p>
        <Button class="{config.buttonClass}" onclick={addToCart}>
          Add to Collection
        </Button>
      </div>
    </div>
    
  {:else if config.layout === 'minimal'}
    <!-- Minimal Card Layout -->
    <div class="flex gap-4 p-4">
      <div class="w-20 h-20 flex-shrink-0">
        {#if product.thumbnail || product?.image_url}
          <LazyImg
            src={product.thumbnail || product?.image_url}
            alt="{product.name} product image"
            class="w-full h-full object-cover"
          />
        {:else}
          <EmptyImage class="w-full h-full object-cover" />
        {/if}
      </div>
      
      <div class="flex-1 min-w-0">
        <h3 class="{config.titleClass}">{product.title}</h3>
        <p class="{config.priceClass}">
          {formatPrice(product.price, page?.data?.store?.currency?.code)}
        </p>
      </div>
      
      <div class="flex-shrink-0">
        <Button size="sm" class="{config.buttonClass}" onclick={addToCart}>
          Add
        </Button>
      </div>
    </div>
    
  {:else if config.layout === 'fashion'}
    <!-- Fashion Card Layout -->
    <div class="relative">
      <!-- Product Image -->
      <div class="relative overflow-hidden">
        {#if product.thumbnail || product?.image_url}
          <LazyImg
            src={product.thumbnail || product?.image_url}
            alt="{product.name} product image"
            class="{config.imageClass}"
          />
        {:else}
          <EmptyImage class="{config.imageClass}" />
        {/if}
        
        <!-- Fashion overlay effects -->
        <div class="absolute inset-0 bg-gradient-to-t from-pink-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        
        <!-- Trending badge -->
        {#if product.tag}
          <div class="absolute top-4 left-4 bg-gradient-to-r from-pink-500 to-purple-600 text-white px-3 py-1 text-xs font-bold rounded-full">
            {product.tag.title}
          </div>
        {/if}
        
        <!-- Quick actions -->
        <div class="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="flex gap-2">
            <button class="w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform" onclick={toggleWishlist}>
              <Heart class="w-4 h-4 text-pink-500" />
            </button>
            <button class="w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform" onclick={handleCardClick}>
              <Eye class="w-4 h-4 text-purple-500" />
            </button>
          </div>
        </div>
      </div>
      
      <!-- Content -->
      <div class="{config.contentClass}">
        <h3 class="{config.titleClass}">{product.title}</h3>
        <p class="{config.priceClass}">
          {formatPrice(product.price, page?.data?.store?.currency?.code)}
        </p>
        <Button class="{config.buttonClass}" onclick={addToCart}>
          <ShoppingCart class="w-4 h-4 mr-2" />
          Add to Cart
        </Button>
      </div>
    </div>
    
  {:else if config.layout === 'tech'}
    <!-- Tech Card Layout -->
    <div class="relative">
      <!-- Product Image -->
      <div class="relative overflow-hidden bg-slate-800">
        {#if product.thumbnail || product?.image_url}
          <LazyImg
            src={product.thumbnail || product?.image_url}
            alt="{product.name} product image"
            class="{config.imageClass}"
          />
        {:else}
          <EmptyImage class="{config.imageClass}" />
        {/if}
        
        <!-- Tech grid overlay -->
        <div class="absolute inset-0 opacity-10">
          <div class="grid grid-cols-8 gap-1 h-full">
            {#each Array(32) as _, i}
              <div class="bg-blue-500 rounded-sm"></div>
            {/each}
          </div>
        </div>
        
        <!-- Tech badge -->
        {#if product.tag}
          <div class="absolute top-4 left-4 bg-blue-600 text-white px-3 py-1 text-xs font-bold uppercase tracking-wider border border-blue-400">
            {product.tag.title}
          </div>
        {/if}
        
        <!-- Specs indicator -->
        <div class="absolute bottom-4 left-4 flex gap-1">
          <div class="w-2 h-2 bg-blue-400 rounded-full"></div>
          <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
          <div class="w-2 h-2 bg-blue-600 rounded-full"></div>
        </div>
      </div>
      
      <!-- Content -->
      <div class="{config.contentClass}">
        <h3 class="{config.titleClass}">{product.title}</h3>
        <div class="flex items-center gap-2 mb-2">
          <div class="text-xs text-blue-300">⚡ Fast Shipping</div>
          <div class="text-xs text-blue-300">🔧 Tech Support</div>
        </div>
        <p class="{config.priceClass}">
          {formatPrice(product.price, page?.data?.store?.currency?.code)}
        </p>
        <Button class="{config.buttonClass}" onclick={addToCart}>
          <Plus class="w-4 h-4 mr-2" />
          Add to Cart
        </Button>
      </div>
    </div>
    
  {:else}
    <!-- Default Card Layout -->
    <div class="relative">
      <!-- Product Image -->
      <div class="relative overflow-hidden">
        {#if product.thumbnail || product?.image_url}
          <LazyImg
            src={product.thumbnail || product?.image_url}
            alt="{product.name} product image"
            class="{config.imageClass}"
          />
        {:else}
          <EmptyImage class="{config.imageClass}" />
        {/if}
        
        {#if product.tag}
          <div class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 text-xs font-bold">
            {product.tag.title}
          </div>
        {/if}
      </div>
      
      <!-- Content -->
      <div class="{config.contentClass}">
        <h3 class="{config.titleClass}">{product.title}</h3>
        <p class="{config.priceClass}">
          {formatPrice(product.price, page?.data?.store?.currency?.code)}
        </p>
        <Button class="{config.buttonClass}" onclick={addToCart}>
          Add to Cart
        </Button>
      </div>
    </div>
  {/if}
</div>

<style>
  /* Theme-specific card styles */
  .theme-card-luxury {
    box-shadow: 0 8px 32px rgba(212, 175, 55, 0.1);
  }
  
  .theme-card-luxury:hover {
    box-shadow: 0 16px 48px rgba(212, 175, 55, 0.2);
  }
  
  .theme-card-fashion {
    box-shadow: 0 8px 32px rgba(236, 72, 153, 0.1);
  }
  
  .theme-card-fashion:hover {
    box-shadow: 0 16px 48px rgba(236, 72, 153, 0.2);
  }
  
  .theme-card-tech {
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.1);
  }
  
  .theme-card-tech:hover {
    box-shadow: 0 16px 48px rgba(59, 130, 246, 0.2);
  }
</style>
