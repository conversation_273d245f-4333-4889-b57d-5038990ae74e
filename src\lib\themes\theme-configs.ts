// Complete Theme Configurations - All 5 themes with section definitions

import type { ThemeConfig } from './types'

// Default Theme Configuration (uses existing components)
export const defaultThemeConfig: ThemeConfig = {
  id: 'default',
  name: 'Default Store',
  description: 'Classic ecommerce design that works for any business type',
  category: 'default',
  preview: '/themes/default-preview.jpg',
  author: 'Svelte Commerce',
  version: '1.0.0',
  sections: [
    {
      id: 'hero',
      component: 'banners',
      variant: 'default',
      order: 1,
      visible: true,
      props: {}
    },
    {
      id: 'collections',
      component: 'collections',
      variant: 'default',
      order: 2,
      visible: true,
      props: {}
    },
    {
      id: 'categories',
      component: 'categories',
      variant: 'default',
      order: 3,
      visible: true,
      props: {}
    },
    {
      id: 'featured-products',
      component: 'featured-products',
      variant: 'default',
      order: 4,
      visible: true,
      props: {}
    }
  ],
  globalStyles: {
    fontFamily: 'Inter, sans-serif',
    primaryColor: '#3b82f6',
    secondaryColor: '#f1f5f9',
    accentColor: '#10b981',
    backgroundColor: '#ffffff',
    textColor: '#1f2937',
    borderRadius: '0.375rem',
    spacing: '1rem',
    shadows: '0 1px 3px 0 rgb(0 0 0 / 0.1)'
  },
  settings: {
    layout: {
      containerMaxWidth: '1200px',
      sectionSpacing: '3rem',
      contentPadding: '1rem'
    },
    animations: {
      enabled: true,
      duration: '300ms',
      easing: 'ease-in-out'
    }
  }
}

// Luxury Theme Configuration
export const luxuryThemeConfig: ThemeConfig = {
  id: 'luxury',
  name: 'Luxury Boutique',
  description: 'Elegant and sophisticated design for premium brands and high-end products',
  category: 'luxury',
  preview: '/themes/luxury-preview.jpg',
  author: 'Svelte Commerce',
  version: '1.0.0',
  sections: [
    {
      id: 'hero',
      component: 'hero',
      variant: 'luxury',
      theme: 'luxury',
      order: 1,
      visible: true,
      props: {
        variant: 'luxury',
        autoplay: true,
        showDots: true,
        showArrows: true
      }
    },
    {
      id: 'collections',
      component: 'collections',
      variant: 'luxury',
      theme: 'luxury',
      order: 2,
      visible: true,
      props: {
        variant: 'luxury',
        showDescription: true,
        layout: 'carousel'
      }
    },
    {
      id: 'categories',
      component: 'categories',
      variant: 'luxury',
      theme: 'luxury',
      order: 3,
      visible: true,
      props: {
        variant: 'luxury',
        showImages: true,
        layout: 'grid'
      }
    },
    {
      id: 'featured-products',
      component: 'featured-products',
      variant: 'luxury',
      theme: 'luxury',
      order: 4,
      visible: true,
      props: {
        variant: 'luxury',
        layout: 'grid'
      }
    },
    {
      id: 'brands',
      component: 'brands',
      variant: 'default',
      order: 5,
      visible: true,
      props: {}
    }
  ],
  globalStyles: {
    fontFamily: 'Playfair Display, serif',
    primaryColor: '#d4af37',
    secondaryColor: '#1a1a1a',
    accentColor: '#b8941f',
    backgroundColor: '#fefbf3',
    textColor: '#1a1a1a',
    borderRadius: '0px',
    spacing: '2rem',
    shadows: '0 10px 25px -3px rgb(0 0 0 / 0.1)'
  },
  customCSS: `
    .luxury-accent { color: #d4af37; }
    .luxury-bg { background: linear-gradient(135deg, #fefbf3 0%, #f7f3e9 100%); }
    .luxury-border { border-color: #d4af37; }
    .luxury-shadow { box-shadow: 0 10px 25px -3px rgba(212, 175, 55, 0.1); }
  `,
  settings: {
    layout: {
      containerMaxWidth: '1400px',
      sectionSpacing: '5rem',
      contentPadding: '2rem'
    },
    typography: {
      headingFont: 'Playfair Display, serif',
      bodyFont: 'Crimson Text, serif',
      fontSize: {
        small: '0.875rem',
        medium: '1rem',
        large: '1.25rem',
        xlarge: '2rem'
      }
    },
    animations: {
      enabled: true,
      duration: '500ms',
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
    }
  }
}

// Minimal Theme Configuration
export const minimalThemeConfig: ThemeConfig = {
  id: 'minimal',
  name: 'Clean Minimal',
  description: 'Simple and functional design focused on content and usability',
  category: 'minimal',
  preview: '/themes/minimal-preview.jpg',
  author: 'Svelte Commerce',
  version: '1.0.0',
  sections: [
    {
      id: 'hero',
      component: 'hero',
      variant: 'minimal',
      theme: 'minimal',
      order: 1,
      visible: true,
      props: {
        variant: 'minimal',
        autoplay: false,
        showDots: false,
        showArrows: true
      }
    },
    {
      id: 'featured-products',
      component: 'featured-products',
      variant: 'minimal',
      theme: 'minimal',
      order: 2,
      visible: true,
      props: {
        variant: 'minimal',
        layout: 'grid'
      }
    },
    {
      id: 'categories',
      component: 'categories',
      variant: 'minimal',
      theme: 'minimal',
      order: 3,
      visible: true,
      props: {
        variant: 'minimal',
        showImages: true,
        layout: 'grid'
      }
    }
  ],
  globalStyles: {
    fontFamily: 'Inter, sans-serif',
    primaryColor: '#000000',
    secondaryColor: '#f5f5f5',
    accentColor: '#6b7280',
    backgroundColor: '#ffffff',
    textColor: '#1f2937',
    borderRadius: '0.25rem',
    spacing: '1rem',
    shadows: '0 1px 2px 0 rgb(0 0 0 / 0.05)'
  },
  customCSS: `
    .minimal-clean { background: #ffffff; border: 1px solid #e5e7eb; }
    .minimal-text { color: #374151; font-weight: 400; }
    .minimal-accent { color: #000000; font-weight: 500; }
    .minimal-border { border: 1px solid #d1d5db; }
  `,
  settings: {
    layout: {
      containerMaxWidth: '1100px',
      sectionSpacing: '2rem',
      contentPadding: '1rem'
    },
    typography: {
      headingFont: 'Inter, sans-serif',
      bodyFont: 'Inter, sans-serif',
      fontSize: {
        small: '0.875rem',
        medium: '1rem',
        large: '1.125rem',
        xlarge: '1.5rem'
      }
    },
    animations: {
      enabled: false,
      duration: '200ms',
      easing: 'ease-out'
    }
  }
}

// Fashion Theme Configuration
export const fashionThemeConfig: ThemeConfig = {
  id: 'fashion',
  name: 'Fashion Forward',
  description: 'Bold and trendy design perfect for fashion brands and lifestyle products',
  category: 'fashion',
  preview: '/themes/fashion-preview.jpg',
  author: 'Svelte Commerce',
  version: '1.0.0',
  sections: [
    {
      id: 'hero',
      component: 'hero',
      variant: 'fashion',
      theme: 'fashion',
      order: 1,
      visible: true,
      props: {
        variant: 'fashion',
        autoplay: true,
        showDots: true,
        showArrows: false
      }
    },
    {
      id: 'collections',
      component: 'collections',
      variant: 'fashion',
      theme: 'fashion',
      order: 2,
      visible: true,
      props: {
        variant: 'fashion',
        showDescription: true,
        layout: 'carousel'
      }
    },
    {
      id: 'categories',
      component: 'categories',
      variant: 'fashion',
      theme: 'fashion',
      order: 3,
      visible: true,
      props: {
        variant: 'fashion',
        showImages: true,
        layout: 'grid'
      }
    },
    {
      id: 'featured-products',
      component: 'featured-products',
      variant: 'fashion',
      theme: 'fashion',
      order: 4,
      visible: true,
      props: {
        variant: 'fashion',
        layout: 'grid'
      }
    }
  ],
  globalStyles: {
    fontFamily: 'Poppins, sans-serif',
    primaryColor: '#ec4899',
    secondaryColor: '#8b5cf6',
    accentColor: '#f97316',
    backgroundColor: '#fdf2f8',
    textColor: '#1f2937',
    borderRadius: '1rem',
    spacing: '1.5rem',
    shadows: '0 20px 25px -5px rgb(0 0 0 / 0.1)'
  },
  customCSS: `
    .fashion-gradient { background: linear-gradient(135deg, #ec4899 0%, #8b5cf6 100%); }
    .fashion-text { background: linear-gradient(135deg, #ec4899 0%, #8b5cf6 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
    .fashion-border { border: 2px solid transparent; background: linear-gradient(white, white) padding-box, linear-gradient(135deg, #ec4899, #8b5cf6) border-box; }
    .fashion-shadow { box-shadow: 0 20px 25px -5px rgba(236, 72, 153, 0.1); }
  `,
  settings: {
    layout: {
      containerMaxWidth: '1300px',
      sectionSpacing: '4rem',
      contentPadding: '1.5rem'
    },
    typography: {
      headingFont: 'Poppins, sans-serif',
      bodyFont: 'Open Sans, sans-serif',
      fontSize: {
        small: '0.875rem',
        medium: '1rem',
        large: '1.25rem',
        xlarge: '2.5rem'
      }
    },
    animations: {
      enabled: true,
      duration: '400ms',
      easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
    }
  }
}

// Tech Theme Configuration
export const techThemeConfig: ThemeConfig = {
  id: 'tech',
  name: 'Tech Store',
  description: 'Clean and technical design perfect for technology and electronics stores',
  category: 'tech',
  preview: '/themes/tech-preview.jpg',
  author: 'Svelte Commerce',
  version: '1.0.0',
  sections: [
    {
      id: 'hero',
      component: 'hero',
      variant: 'tech',
      theme: 'tech',
      order: 1,
      visible: true,
      props: {
        variant: 'tech',
        autoplay: true,
        showDots: false,
        showArrows: true
      }
    },
    {
      id: 'categories',
      component: 'categories',
      variant: 'tech',
      theme: 'tech',
      order: 2,
      visible: true,
      props: {
        variant: 'tech',
        showImages: true,
        layout: 'grid'
      }
    },
    {
      id: 'featured-products',
      component: 'featured-products',
      variant: 'tech',
      theme: 'tech',
      order: 3,
      visible: true,
      props: {
        variant: 'tech',
        layout: 'grid'
      }
    },
    {
      id: 'brands',
      component: 'brands',
      variant: 'default',
      order: 4,
      visible: true,
      props: {}
    }
  ],
  globalStyles: {
    fontFamily: 'Inter, sans-serif',
    primaryColor: '#3b82f6',
    secondaryColor: '#1e40af',
    accentColor: '#06b6d4',
    backgroundColor: '#f8fafc',
    textColor: '#1e293b',
    borderRadius: '0.5rem',
    spacing: '1.25rem',
    shadows: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
  },
  customCSS: `
    .tech-gradient { background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%); }
    .tech-border { border: 1px solid #3b82f6; }
    .tech-shadow { box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.1); }
    .tech-accent { color: #3b82f6; }
    .tech-bg { background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); }
  `,
  settings: {
    layout: {
      containerMaxWidth: '1200px',
      sectionSpacing: '3rem',
      contentPadding: '1.25rem'
    },
    typography: {
      headingFont: 'Inter, sans-serif',
      bodyFont: 'Inter, sans-serif',
      fontSize: {
        small: '0.875rem',
        medium: '1rem',
        large: '1.125rem',
        xlarge: '2rem'
      }
    },
    animations: {
      enabled: true,
      duration: '300ms',
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
    }
  }
}

// All available themes
export const availableThemes: ThemeConfig[] = [
  defaultThemeConfig,
  luxuryThemeConfig,
  minimalThemeConfig,
  fashionThemeConfig,
  techThemeConfig
]

// Theme categories for organization
export const themeCategories = {
  default: {
    name: 'Default',
    description: 'Classic designs that work for any business',
    icon: '🏪'
  },
  luxury: {
    name: 'Luxury',
    description: 'Elegant designs for premium brands',
    icon: '💎'
  },
  minimal: {
    name: 'Minimal',
    description: 'Clean and simple designs',
    icon: '⚪'
  },
  fashion: {
    name: 'Fashion',
    description: 'Bold designs for fashion brands',
    icon: '👗'
  },
  tech: {
    name: 'Technology',
    description: 'Modern designs for tech products',
    icon: '💻'
  }
}

// Helper functions
export function getThemeById(themeId: string): ThemeConfig | undefined {
  return availableThemes.find(theme => theme.id === themeId)
}

export function getThemesByCategory(category: string): ThemeConfig[] {
  return availableThemes.filter(theme => theme.category === category)
}

export function getDefaultTheme(): ThemeConfig {
  return defaultThemeConfig
}
