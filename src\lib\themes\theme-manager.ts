// Complete Theme Manager - Handles theme switching, persistence, and state management

import { writable, derived, get } from 'svelte/store'
import type { 
  ThemeConfig, 
  ThemeManagerState, 
  ThemePreferences, 
  ThemeChangeEvent,
  SectionConfig,
  ThemeValidationResult
} from './types'

// Theme manager class with complete functionality
export class ThemeManager {
  private state = writable<ThemeManagerState>({
    currentTheme: null as any, // Will be set in constructor
    availableThemes: [],
    isLoading: false,
    error: undefined,
    previewMode: false,
    previewTheme: undefined
  })

  // Public stores
  public readonly currentTheme = derived(this.state, $state => $state.currentTheme)
  public readonly availableThemes = derived(this.state, $state => $state.availableThemes)
  public readonly isLoading = derived(this.state, $state => $state.isLoading)
  public readonly error = derived(this.state, $state => $state.error)
  public readonly previewMode = derived(this.state, $state => $state.previewMode)

  // Event listeners
  private listeners: {
    themeChange: ((event: ThemeChangeEvent) => void)[]
    error: ((error: string) => void)[]
  } = {
    themeChange: [],
    error: []
  }

  constructor(availableThemes: ThemeConfig[], defaultThemeId?: string) {
    this.state.update(state => ({
      ...state,
      availableThemes,
      currentTheme: this.getDefaultTheme(availableThemes, defaultThemeId)
    }))

    // Load saved theme on initialization
    this.loadSavedTheme()
    
    // Apply initial theme styles
    this.applyGlobalStyles()
  }

  // Get default theme
  private getDefaultTheme(themes: ThemeConfig[], defaultId?: string): ThemeConfig {
    if (defaultId) {
      const theme = themes.find(t => t.id === defaultId)
      if (theme) return theme
    }
    
    // Fallback to first theme or create minimal default
    return themes[0] || {
      id: 'default',
      name: 'Default',
      description: 'Default theme',
      category: 'default',
      preview: '',
      sections: [],
      globalStyles: {
        fontFamily: 'Inter, sans-serif',
        primaryColor: '#3b82f6',
        secondaryColor: '#f1f5f9',
        borderRadius: '0.375rem'
      }
    }
  }

  // Load saved theme from localStorage
  private loadSavedTheme(): void {
    try {
      const savedPreferences = localStorage.getItem('theme-preferences')
      if (savedPreferences) {
        const preferences: ThemePreferences = JSON.parse(savedPreferences)
        const theme = get(this.availableThemes).find(t => t.id === preferences.themeId)
        
        if (theme) {
          this.state.update(state => ({
            ...state,
            currentTheme: theme
          }))
        }
      }
    } catch (error) {
      console.warn('Failed to load saved theme:', error)
      this.emitError('Failed to load saved theme preferences')
    }
  }

  // Save theme preferences
  private saveThemePreferences(themeId: string): void {
    try {
      const preferences: ThemePreferences = {
        themeId,
        lastModified: new Date().toISOString()
      }
      localStorage.setItem('theme-preferences', JSON.stringify(preferences))
    } catch (error) {
      console.warn('Failed to save theme preferences:', error)
      this.emitError('Failed to save theme preferences')
    }
  }

  // Switch to a different theme
  public async switchTheme(themeId: string): Promise<void> {
    this.state.update(state => ({ ...state, isLoading: true, error: undefined }))

    try {
      const newTheme = get(this.availableThemes).find(t => t.id === themeId)
      
      if (!newTheme) {
        throw new Error(`Theme with id "${themeId}" not found`)
      }

      // Validate theme before switching
      const validation = this.validateTheme(newTheme)
      if (!validation.isValid) {
        throw new Error(`Invalid theme: ${validation.errors.join(', ')}`)
      }

      const previousTheme = get(this.currentTheme)

      // Update state
      this.state.update(state => ({
        ...state,
        currentTheme: newTheme,
        isLoading: false,
        previewMode: false,
        previewTheme: undefined
      }))

      // Apply theme styles
      this.applyGlobalStyles()

      // Save preferences
      this.saveThemePreferences(themeId)

      // Emit theme change event
      this.emitThemeChange({
        previousTheme,
        newTheme,
        timestamp: Date.now()
      })

    } catch (error) {
      this.state.update(state => ({
        ...state,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }))
      this.emitError(error instanceof Error ? error.message : 'Unknown error occurred')
    }
  }

  // Preview theme without saving
  public previewTheme(themeId: string): void {
    const theme = get(this.availableThemes).find(t => t.id === themeId)
    
    if (!theme) {
      this.emitError(`Theme with id "${themeId}" not found`)
      return
    }

    this.state.update(state => ({
      ...state,
      previewMode: true,
      previewTheme: theme
    }))

    // Apply preview styles temporarily
    this.applyGlobalStyles(theme)
  }

  // Exit preview mode
  public exitPreview(): void {
    this.state.update(state => ({
      ...state,
      previewMode: false,
      previewTheme: undefined
    }))

    // Restore original theme styles
    this.applyGlobalStyles()
  }

  // Apply global theme styles to document
  public applyGlobalStyles(theme?: ThemeConfig): void {
    const targetTheme = theme || get(this.currentTheme)
    if (!targetTheme) return

    const root = document.documentElement
    const styles = targetTheme.globalStyles

    // Apply CSS custom properties
    root.style.setProperty('--theme-font-family', styles.fontFamily)
    root.style.setProperty('--theme-primary-color', styles.primaryColor)
    root.style.setProperty('--theme-secondary-color', styles.secondaryColor)
    root.style.setProperty('--theme-border-radius', styles.borderRadius)

    if (styles.accentColor) {
      root.style.setProperty('--theme-accent-color', styles.accentColor)
    }
    if (styles.backgroundColor) {
      root.style.setProperty('--theme-background-color', styles.backgroundColor)
    }
    if (styles.textColor) {
      root.style.setProperty('--theme-text-color', styles.textColor)
    }
    if (styles.spacing) {
      root.style.setProperty('--theme-spacing', styles.spacing)
    }
    if (styles.shadows) {
      root.style.setProperty('--theme-shadows', styles.shadows)
    }

    // Apply theme class to body
    document.body.className = document.body.className.replace(/theme-\w+/g, '')
    document.body.classList.add(`theme-${targetTheme.id}`)

    // Apply custom CSS if provided
    if (targetTheme.customCSS) {
      this.applyCustomCSS(targetTheme.customCSS)
    }
  }

  // Apply custom CSS
  private applyCustomCSS(css: string): void {
    // Remove existing custom theme CSS
    const existingStyle = document.getElementById('theme-custom-css')
    if (existingStyle) {
      existingStyle.remove()
    }

    // Add new custom CSS
    const style = document.createElement('style')
    style.id = 'theme-custom-css'
    style.textContent = css
    document.head.appendChild(style)
  }

  // Validate theme configuration
  public validateTheme(theme: ThemeConfig): ThemeValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // Required fields validation
    if (!theme.id) errors.push('Theme ID is required')
    if (!theme.name) errors.push('Theme name is required')
    if (!theme.sections) errors.push('Theme sections are required')
    if (!theme.globalStyles) errors.push('Theme global styles are required')

    // Global styles validation
    if (theme.globalStyles) {
      if (!theme.globalStyles.fontFamily) errors.push('Font family is required')
      if (!theme.globalStyles.primaryColor) errors.push('Primary color is required')
      if (!theme.globalStyles.secondaryColor) errors.push('Secondary color is required')
      if (!theme.globalStyles.borderRadius) errors.push('Border radius is required')
    }

    // Sections validation
    if (theme.sections) {
      theme.sections.forEach((section, index) => {
        if (!section.id) errors.push(`Section ${index} is missing ID`)
        if (!section.component) errors.push(`Section ${index} is missing component`)
        if (!section.variant) errors.push(`Section ${index} is missing variant`)
        if (typeof section.order !== 'number') errors.push(`Section ${index} is missing order`)
        if (typeof section.visible !== 'boolean') errors.push(`Section ${index} is missing visible property`)
      })

      // Check for duplicate section IDs
      const sectionIds = theme.sections.map(s => s.id)
      const duplicates = sectionIds.filter((id, index) => sectionIds.indexOf(id) !== index)
      if (duplicates.length > 0) {
        errors.push(`Duplicate section IDs found: ${duplicates.join(', ')}`)
      }
    }

    // Warnings
    if (!theme.description) warnings.push('Theme description is recommended')
    if (!theme.preview) warnings.push('Theme preview image is recommended')
    if (!theme.author) warnings.push('Theme author is recommended')
    if (!theme.version) warnings.push('Theme version is recommended')

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  // Get sections for current theme
  public getSections(): SectionConfig[] {
    const theme = get(this.previewMode) ? get(this.state).previewTheme : get(this.currentTheme)
    if (!theme) return []

    return theme.sections
      .filter(section => section.visible)
      .sort((a, b) => a.order - b.order)
  }

  // Add section to current theme
  public addSection(section: Omit<SectionConfig, 'order'>): void {
    const currentTheme = get(this.currentTheme)
    if (!currentTheme) return

    const maxOrder = Math.max(...currentTheme.sections.map(s => s.order), 0)
    const newSection: SectionConfig = {
      ...section,
      order: maxOrder + 1
    }

    const updatedTheme = {
      ...currentTheme,
      sections: [...currentTheme.sections, newSection]
    }

    this.state.update(state => ({
      ...state,
      currentTheme: updatedTheme
    }))
  }

  // Remove section from current theme
  public removeSection(sectionId: string): void {
    const currentTheme = get(this.currentTheme)
    if (!currentTheme) return

    const updatedTheme = {
      ...currentTheme,
      sections: currentTheme.sections.filter(s => s.id !== sectionId)
    }

    this.state.update(state => ({
      ...state,
      currentTheme: updatedTheme
    }))
  }

  // Update section configuration
  public updateSection(sectionId: string, updates: Partial<SectionConfig>): void {
    const currentTheme = get(this.currentTheme)
    if (!currentTheme) return

    const updatedTheme = {
      ...currentTheme,
      sections: currentTheme.sections.map(section =>
        section.id === sectionId ? { ...section, ...updates } : section
      )
    }

    this.state.update(state => ({
      ...state,
      currentTheme: updatedTheme
    }))
  }

  // Reorder sections
  public reorderSections(sectionIds: string[]): void {
    const currentTheme = get(this.currentTheme)
    if (!currentTheme) return

    const reorderedSections = sectionIds.map((id, index) => {
      const section = currentTheme.sections.find(s => s.id === id)
      if (section) {
        return { ...section, order: index + 1 }
      }
      return null
    }).filter(Boolean) as SectionConfig[]

    const updatedTheme = {
      ...currentTheme,
      sections: reorderedSections
    }

    this.state.update(state => ({
      ...state,
      currentTheme: updatedTheme
    }))
  }

  // Event listeners
  public onThemeChange(callback: (event: ThemeChangeEvent) => void): () => void {
    this.listeners.themeChange.push(callback)
    return () => {
      const index = this.listeners.themeChange.indexOf(callback)
      if (index > -1) {
        this.listeners.themeChange.splice(index, 1)
      }
    }
  }

  public onError(callback: (error: string) => void): () => void {
    this.listeners.error.push(callback)
    return () => {
      const index = this.listeners.error.indexOf(callback)
      if (index > -1) {
        this.listeners.error.splice(index, 1)
      }
    }
  }

  // Emit events
  private emitThemeChange(event: ThemeChangeEvent): void {
    this.listeners.themeChange.forEach(callback => {
      try {
        callback(event)
      } catch (error) {
        console.error('Error in theme change listener:', error)
      }
    })
  }

  private emitError(error: string): void {
    this.listeners.error.forEach(callback => {
      try {
        callback(error)
      } catch (err) {
        console.error('Error in error listener:', err)
      }
    })
  }

  // Get theme by ID
  public getThemeById(themeId: string): ThemeConfig | undefined {
    return get(this.availableThemes).find(theme => theme.id === themeId)
  }

  // Get current theme ID
  public getCurrentThemeId(): string {
    return get(this.currentTheme)?.id || 'default'
  }

  // Check if theme exists
  public hasTheme(themeId: string): boolean {
    return get(this.availableThemes).some(theme => theme.id === themeId)
  }

  // Get themes by category
  public getThemesByCategory(category: string): ThemeConfig[] {
    return get(this.availableThemes).filter(theme => theme.category === category)
  }

  // Reset to default theme
  public resetToDefault(): void {
    const defaultTheme = get(this.availableThemes).find(t => t.category === 'default')
    if (defaultTheme) {
      this.switchTheme(defaultTheme.id)
    }
  }

  // Export current theme configuration
  public exportTheme(): string {
    const currentTheme = get(this.currentTheme)
    if (!currentTheme) {
      throw new Error('No current theme to export')
    }
    return JSON.stringify(currentTheme, null, 2)
  }

  // Import theme configuration
  public importTheme(themeJson: string): void {
    try {
      const theme: ThemeConfig = JSON.parse(themeJson)
      const validation = this.validateTheme(theme)

      if (!validation.isValid) {
        throw new Error(`Invalid theme: ${validation.errors.join(', ')}`)
      }

      // Add to available themes if not exists
      const existingTheme = this.getThemeById(theme.id)
      if (!existingTheme) {
        this.state.update(state => ({
          ...state,
          availableThemes: [...state.availableThemes, theme]
        }))
      }

      // Switch to imported theme
      this.switchTheme(theme.id)
    } catch (error) {
      this.emitError(error instanceof Error ? error.message : 'Failed to import theme')
    }
  }

  // Cleanup
  public destroy(): void {
    this.listeners.themeChange = []
    this.listeners.error = []

    // Remove custom CSS
    const customStyle = document.getElementById('theme-custom-css')
    if (customStyle) {
      customStyle.remove()
    }

    // Remove theme class from body
    document.body.className = document.body.className.replace(/theme-\w+/g, '')
  }
}
