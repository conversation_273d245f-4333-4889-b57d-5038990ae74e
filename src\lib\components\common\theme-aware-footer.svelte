<script lang="ts">
  // Theme-Aware Footer - Renders completely different footer styles based on current theme
  
  import { getGlobalThemeManager } from '$lib/themes'
  import { page } from '$app/state'
  import { onMount } from 'svelte'
  import { ChevronDown } from 'lucide-svelte'
  import TrustpilotPlugin from '$lib/core/components/plugins/trustpilot-plugin.svelte'
  import masterCard from '$lib/assets/payment-methods/mastercard.png'
  import paypal from '$lib/assets/payment-methods/paypal.png'
  import skrill from '$lib/assets/payment-methods/skrill.png'
  import visa from '$lib/assets/payment-methods/visa.png'
  
  let currentTheme = $state('default')
  let themeManager: any = null
  let isExpanded = $state(false)
  
  let paymentMethodCards = [masterCard, paypal, skrill, visa]
  
  onMount(() => {
    themeManager = getGlobalThemeManager()
    if (themeManager) {
      // Subscribe to theme changes
      const unsubscribe = themeManager.currentTheme.subscribe((theme: any) => {
        currentTheme = theme?.id || 'default'
      })
      
      return unsubscribe
    }
  })
  
  // Theme-specific footer configurations
  const footerVariants = {
    default: {
      containerClass: 'mt-8 border-t bg-white',
      contentClass: 'mx-auto w-full max-w-screen-xl xl:pb-2',
      logoClass: 'mr-2 h-10 object-contain',
      titleClass: 'text-2xl font-semibold dark:text-white',
      descriptionClass: 'prose text-sm text-gray-600',
      linkClass: 'text-sm text-gray-400 transition-colors hover:text-gray-900',
      copyrightClass: 'text-sm text-gray-500 dark:text-gray-400 sm:text-center',
      layout: 'standard'
    },
    luxury: {
      containerClass: 'mt-16 bg-gradient-to-b from-amber-50 to-amber-100 border-t-2 border-amber-200',
      contentClass: 'mx-auto w-full max-w-screen-xl xl:pb-4',
      logoClass: 'mr-3 h-12 object-contain',
      titleClass: 'text-3xl font-serif font-bold text-amber-800',
      descriptionClass: 'prose text-sm text-amber-700 font-serif leading-relaxed',
      linkClass: 'text-sm text-amber-600 transition-colors hover:text-amber-800 font-serif',
      copyrightClass: 'text-sm text-amber-600 font-serif',
      layout: 'elegant'
    },
    minimal: {
      containerClass: 'mt-4 border-t border-gray-100 bg-gray-50',
      contentClass: 'mx-auto w-full max-w-4xl',
      logoClass: 'mr-2 h-8 object-contain',
      titleClass: 'text-lg font-light text-gray-900',
      descriptionClass: 'text-xs text-gray-500 font-light',
      linkClass: 'text-xs text-gray-400 transition-colors hover:text-gray-900 font-light',
      copyrightClass: 'text-xs text-gray-400 font-light',
      layout: 'minimal'
    },
    fashion: {
      containerClass: 'mt-12 bg-gradient-to-r from-pink-50 via-white to-purple-50 border-t-4 border-gradient-to-r from-pink-300 to-purple-300',
      contentClass: 'mx-auto w-full max-w-screen-xl xl:pb-4',
      logoClass: 'mr-3 h-11 object-contain',
      titleClass: 'text-2xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent',
      descriptionClass: 'prose text-sm text-gray-700 font-medium',
      linkClass: 'text-sm text-pink-600 transition-colors hover:text-purple-600 font-medium',
      copyrightClass: 'text-sm text-gray-600 font-medium',
      layout: 'fashion'
    },
    tech: {
      containerClass: 'mt-8 bg-slate-900 text-white border-t-2 border-blue-500',
      contentClass: 'mx-auto w-full max-w-screen-xl xl:pb-4',
      logoClass: 'mr-2 h-10 object-contain filter brightness-0 invert',
      titleClass: 'text-2xl font-bold text-blue-400',
      descriptionClass: 'prose text-sm text-gray-300',
      linkClass: 'text-sm text-blue-300 transition-colors hover:text-blue-100',
      copyrightClass: 'text-sm text-gray-400',
      layout: 'tech'
    }
  }
  
  const config = footerVariants[currentTheme] || footerVariants.default
</script>

<!-- Theme-specific footer wrapper -->
<div class="theme-footer theme-{currentTheme}" data-theme={currentTheme}>
  <footer class="{config.containerClass}">
    <div class="{config.contentClass}">
      {#if page?.data?.store?.plugins?.footerSettings?.collapseOnMobile}
        <button
          class="flex w-full items-center justify-between p-4 text-sm font-medium uppercase tracking-tighter md:hidden {config.linkClass}"
          onclick={() => (isExpanded = !isExpanded)}
        >
          <span>More about {page?.data?.store?.name}</span>
          <ChevronDown size={20} class="transition-transform duration-200 {isExpanded ? 'rotate-180' : ''}" />
        </button>
      {/if}

      <div class="overflow-hidden {page?.data?.store?.plugins?.footerSettings?.collapseOnMobile ? (isExpanded ? '' : 'hidden md:block') : ''}">
        
        {#if config.layout === 'luxury'}
          <!-- Luxury Footer Layout -->
          <div class="px-8 py-16">
            <div class="text-center mb-12">
              <a href="/" class="inline-flex items-center justify-center mb-6">
                {#if page?.data?.store?.logo}
                  <img src={page?.data?.store?.logo} class="{config.logoClass}" alt="Logo" />
                {:else}
                  <span class="{config.titleClass}"> {page?.data?.store?.name} </span>
                {/if}
              </a>
              <p class="{config.descriptionClass} max-w-2xl mx-auto">{@html page?.data?.store?.description}</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
              {#each page?.data?.store?.menu?.find?.((menu) => menu?.menuId === 'footer')?.items || [] as item}
                <div class="text-center">
                  <h3 class="text-amber-800 mb-4 text-lg font-serif font-semibold uppercase tracking-wider">{item?.name}</h3>
                  {#if item?.items?.length > 0}
                    <ul class="space-y-3">
                      {#each item.items as child}
                        <li>
                          <a href={child.link || '#'} class="{config.linkClass} block">{child.name}</a>
                        </li>
                      {/each}
                    </ul>
                  {/if}
                </div>
              {/each}
            </div>
          </div>
          
        {:else if config.layout === 'minimal'}
          <!-- Minimal Footer Layout -->
          <div class="px-4 py-8">
            <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
              <div class="flex items-center">
                {#if page?.data?.store?.logo}
                  <img src={page?.data?.store?.logo} class="{config.logoClass}" alt="Logo" />
                {:else}
                  <span class="{config.titleClass}"> {page?.data?.store?.name} </span>
                {/if}
              </div>
              
              <div class="flex space-x-8">
                {#each page?.data?.store?.menu?.find?.((menu) => menu?.menuId === 'footer')?.items || [] as item}
                  {#if item?.items?.length > 0}
                    {#each item.items.slice(0, 3) as child}
                      <a href={child.link || '#'} class="{config.linkClass}">{child.name}</a>
                    {/each}
                  {/if}
                {/each}
              </div>
            </div>
          </div>
          
        {:else if config.layout === 'fashion'}
          <!-- Fashion Footer Layout -->
          <div class="px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
              <div class="lg:col-span-2">
                <a href="/" class="inline-flex items-center mb-4">
                  {#if page?.data?.store?.logo}
                    <img src={page?.data?.store?.logo} class="{config.logoClass}" alt="Logo" />
                  {:else}
                    <span class="{config.titleClass}"> {page?.data?.store?.name} </span>
                  {/if}
                </a>
                <p class="{config.descriptionClass} mb-6">{@html page?.data?.store?.description}</p>
                <div class="flex space-x-4">
                  <div class="w-8 h-8 rounded-full bg-gradient-to-r from-pink-400 to-purple-500 flex items-center justify-center">
                    <span class="text-white text-sm">📧</span>
                  </div>
                  <div class="w-8 h-8 rounded-full bg-gradient-to-r from-purple-400 to-pink-500 flex items-center justify-center">
                    <span class="text-white text-sm">📱</span>
                  </div>
                </div>
              </div>
              
              {#each page?.data?.store?.menu?.find?.((menu) => menu?.menuId === 'footer')?.items || [] as item}
                <div>
                  <h3 class="text-gray-900 mb-4 text-lg font-bold uppercase tracking-wide">{item?.name}</h3>
                  {#if item?.items?.length > 0}
                    <ul class="space-y-2">
                      {#each item.items as child}
                        <li>
                          <a href={child.link || '#'} class="{config.linkClass}">{child.name}</a>
                        </li>
                      {/each}
                    </ul>
                  {/if}
                </div>
              {/each}
            </div>
          </div>
          
        {:else if config.layout === 'tech'}
          <!-- Tech Footer Layout -->
          <div class="px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
              <div>
                <a href="/" class="inline-flex items-center mb-4">
                  {#if page?.data?.store?.logo}
                    <img src={page?.data?.store?.logo} class="{config.logoClass}" alt="Logo" />
                  {:else}
                    <span class="{config.titleClass}"> {page?.data?.store?.name} </span>
                  {/if}
                </a>
                <p class="{config.descriptionClass} mb-4">{@html page?.data?.store?.description}</p>
                <div class="flex space-x-2">
                  <div class="px-3 py-1 bg-blue-600 rounded text-xs">TECH</div>
                  <div class="px-3 py-1 bg-blue-700 rounded text-xs">24/7</div>
                </div>
              </div>
              
              {#each page?.data?.store?.menu?.find?.((menu) => menu?.menuId === 'footer')?.items?.slice(0, 2) || [] as item}
                <div>
                  <h3 class="text-blue-400 mb-4 text-lg font-bold uppercase tracking-wide">{item?.name}</h3>
                  {#if item?.items?.length > 0}
                    <ul class="space-y-2">
                      {#each item.items as child}
                        <li>
                          <a href={child.link || '#'} class="{config.linkClass}">{child.name}</a>
                        </li>
                      {/each}
                    </ul>
                  {/if}
                </div>
              {/each}
            </div>
          </div>
          
        {:else}
          <!-- Default Footer Layout -->
          <div class="gap-4 p-4 px-8 py-8 md:flex md:justify-between md:py-16">
            <div class="mb-12 flex max-w-xs flex-col gap-4">
              <a href="/" class="flex items-center">
                {#if page?.data?.store?.logo}
                  <img src={page?.data?.store?.logo} class="{config.logoClass}" alt="Logo" />
                {:else}
                  <span class="{config.titleClass}"> {page?.data?.store?.name} </span>
                {/if}
              </a>
              <p class="{config.descriptionClass}">{@html page?.data?.store?.description}</p>
            </div>
            
            <div class="grid grid-cols-2 gap-8 sm:grid-cols-3 sm:gap-10">
              {#each page?.data?.store?.menu?.find?.((menu) => menu?.menuId === 'footer')?.items || [] as item}
                <div>
                  <h1 class="text-black-200 mb-4 text-lg font-semibold">{item?.name}</h1>
                  {#if item?.items?.length > 0}
                    <ul class="space-y-2">
                      {#each item.items as child}
                        <li>
                          <a href={child.link || '#'} class="{config.linkClass}">{child.name}</a>
                        </li>
                      {/each}
                    </ul>
                  {/if}
                </div>
              {/each}
            </div>
          </div>
        {/if}

        <!-- Footer Bottom Section -->
        <div class="border-t {currentTheme === 'tech' ? 'border-gray-700' : currentTheme === 'luxury' ? 'border-amber-200' : 'border-gray-200'} px-8 py-6">
          <div class="flex w-full flex-col-reverse items-start justify-between gap-2 max-sm:mt-2 sm:flex-row sm:items-center sm:gap-0">
            <span class="{config.copyrightClass}">
              Copyright {new Date().getFullYear()} 
              <a href="/" class="cursor-pointer hover:underline">
                {page?.data?.store?.name}
              </a>
              . All Rights Reserved.
            </span>
            
            <TrustpilotPlugin />

            {#if paymentMethodCards?.length}
              <ul class="col-span-1 m-0 flex list-none flex-wrap items-center justify-end gap-2 p-0">
                {#each paymentMethodCards as pmc}
                  <li>
                    <img src={pmc} alt="" class="h-8 w-auto object-contain {currentTheme === 'tech' ? 'filter brightness-0 invert opacity-70' : ''}" />
                  </li>
                {/each}
              </ul>
            {/if}
          </div>
        </div>
      </div>
    </div>
  </footer>
</div>

<style>
  /* Theme-specific footer styles */
  .theme-footer-luxury {
    background: linear-gradient(135deg, #fefbf3 0%, #f7f3e9 100%);
  }
  
  .theme-footer-minimal {
    background: #f9fafb;
  }
  
  .theme-footer-fashion {
    background: linear-gradient(135deg, #fdf2f8 0%, #f3e8ff 100%);
  }
  
  .theme-footer-tech {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }
</style>
