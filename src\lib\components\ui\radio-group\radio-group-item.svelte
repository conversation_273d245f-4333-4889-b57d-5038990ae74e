<script lang="ts">
	import { RadioGroup as RadioGroupPrimitive, type WithoutChildrenOrChild } from 'bits-ui'
	import Circle from 'lucide-svelte/icons/circle'
	import { cn } from '$lib/core/utils'

	let {
		ref = $bindable(null),
		class: className,
		...restProps
	}: WithoutChildrenOrChild<RadioGroupPrimitive.ItemProps> & {
		value: string
	} = $props()
</script>

<RadioGroupPrimitive.Item
	bind:ref
	class={cn(
		'aspect-square size-4 rounded-full border border-primary text-primary shadow focus:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50',
		className
	)}
	{...restProps}
>
	{#snippet children({ checked })}
		<div class="flex items-center justify-center">
			{#if checked}
				<Circle class="size-3.5 fill-primary" />
			{/if}
		</div>
	{/snippet}
</RadioGroupPrimitive.Item>
