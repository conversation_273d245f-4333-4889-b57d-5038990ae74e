<script lang="ts">
	import { RangeCalendar as RangeCalendarPrimitive } from 'bits-ui'
	import ChevronLeft from 'lucide-svelte/icons/chevron-left'
	import { buttonVariants } from '$lib/components/ui/button/index.js'
	import { cn } from '$lib/core/utils'
	let { ref = $bindable(null), class: className, children, ...restProps }: RangeCalendarPrimitive.PrevButtonProps = $props()
</script>

{#snippet Fallback()}
	<ChevronLeft />
{/snippet}

<RangeCalendarPrimitive.PrevButton
	bind:ref
	class={cn(buttonVariants({ variant: 'outline' }), 'size-7 bg-transparent p-0 opacity-50 hover:opacity-100', className)}
	{...restProps}
	children={children || Fallback}
/>
