<script lang="ts">
  // Enhanced Banners Component - Hero sections with theme variants
  
  import { onMount, onDestroy } from 'svelte'
  import Banners from './banners.svelte'
  import { Carousel, CarouselContent, CarouselItem } from '$lib/components/ui/carousel/index.js'
  import CarouselPrevious from '$lib/components/ui/carousel/carousel-previous.svelte'
  import CarouselNext from '$lib/components/ui/carousel/carousel-next.svelte'
  import { Button } from '$lib/components/ui/button'
  
  let { 
    variant = 'default',
    theme = 'default',
    sliderBannersDesktop = [],
    sliderBannersMobile = [],
    autoplay = false,
    showDots = true,
    showArrows = true,
    ...props 
  } = $props()
  
  let currentSlide = $state(0)
  let autoplayInterval: NodeJS.Timeout | null = null
  let carouselApi: any = null
  
  // Variant configurations
  const variants = {
    luxury: {
      containerClass: 'luxury-hero relative w-full min-h-[85vh] overflow-hidden',
      overlayClass: 'absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-black/50 flex items-center justify-center z-10',
      contentClass: 'text-center text-white max-w-4xl px-8',
      titleClass: 'font-serif text-5xl md:text-7xl font-bold mb-6 text-amber-300 drop-shadow-2xl leading-tight',
      subtitleClass: 'font-serif text-xl md:text-2xl mb-8 text-gray-100 leading-relaxed max-w-2xl mx-auto',
      buttonClass: 'bg-gradient-to-r from-amber-400 to-amber-600 text-black px-12 py-4 font-bold uppercase tracking-widest hover:from-amber-500 hover:to-amber-700 transition-all duration-300 shadow-2xl border-2 border-amber-300 rounded-none',
      imageClass: 'w-full h-full object-cover transition-transform duration-[8s] hover:scale-105',
      showOverlay: true,
      autoplay: true,
      autoplayDelay: 6000,
      transition: 'fade'
    },
    minimal: {
      containerClass: 'minimal-hero relative w-full min-h-[60vh] bg-gray-50',
      overlayClass: 'absolute inset-0 flex items-center justify-start pl-8 md:pl-16 z-10',
      contentClass: 'text-gray-900 max-w-lg',
      titleClass: 'font-sans text-3xl md:text-5xl font-light mb-6 leading-tight text-gray-900',
      subtitleClass: 'font-sans text-lg md:text-xl mb-8 text-gray-600 leading-relaxed',
      buttonClass: 'bg-black text-white px-8 py-3 font-medium hover:bg-gray-800 transition-colors duration-200 border border-black rounded-none',
      imageClass: 'w-full h-full object-cover opacity-90',
      showOverlay: true,
      autoplay: false,
      transition: 'slide'
    },
    fashion: {
      containerClass: 'fashion-hero relative w-full min-h-[90vh] overflow-hidden',
      overlayClass: 'absolute inset-0 bg-gradient-to-r from-pink-500/20 via-transparent to-purple-600/20 flex items-center justify-center z-10',
      contentClass: 'text-center text-white max-w-3xl px-8',
      titleClass: 'font-bold text-4xl md:text-6xl mb-6 bg-gradient-to-r from-pink-400 to-purple-500 bg-clip-text text-transparent drop-shadow-lg leading-tight',
      subtitleClass: 'font-medium text-lg md:text-xl mb-8 text-white/90 leading-relaxed',
      buttonClass: 'bg-gradient-to-r from-pink-500 to-purple-600 text-white px-12 py-4 rounded-full font-bold hover:from-pink-600 hover:to-purple-700 transition-all duration-300 shadow-xl transform hover:scale-105',
      imageClass: 'w-full h-full object-cover transition-transform duration-[10s] hover:scale-110',
      showOverlay: true,
      autoplay: true,
      autoplayDelay: 4000,
      transition: 'zoom'
    },
    tech: {
      containerClass: 'tech-hero relative w-full min-h-[70vh] bg-slate-900',
      overlayClass: 'absolute inset-0 bg-gradient-to-r from-blue-900/80 to-transparent flex items-center justify-start pl-8 md:pl-16 z-10',
      contentClass: 'text-white max-w-2xl',
      titleClass: 'font-sans text-4xl md:text-6xl font-bold mb-6 text-blue-400 leading-tight',
      subtitleClass: 'font-sans text-lg md:text-xl mb-8 text-gray-300 leading-relaxed',
      buttonClass: 'bg-blue-600 text-white px-10 py-3 font-semibold hover:bg-blue-700 transition-colors duration-200 border border-blue-500 rounded-md',
      imageClass: 'w-full h-full object-cover',
      showOverlay: true,
      autoplay: true,
      autoplayDelay: 5000,
      transition: 'slide'
    }
  }
  
  const config = variants[variant] || {
    containerClass: 'relative w-full min-h-[50vh]',
    showOverlay: false,
    autoplay: false,
    transition: 'slide'
  }
  
  // Auto-play functionality
  function startAutoplay() {
    if (!config.autoplay || !sliderBannersDesktop?.length || sliderBannersDesktop.length <= 1) return
    
    autoplayInterval = setInterval(() => {
      if (carouselApi) {
        carouselApi.scrollNext()
      } else {
        currentSlide = (currentSlide + 1) % sliderBannersDesktop.length
      }
    }, config.autoplayDelay || 5000)
  }
  
  function stopAutoplay() {
    if (autoplayInterval) {
      clearInterval(autoplayInterval)
      autoplayInterval = null
    }
  }
  
  function goToSlide(index: number) {
    currentSlide = index
    if (carouselApi) {
      carouselApi.scrollTo(index)
    }
    stopAutoplay()
    if (config.autoplay) {
      setTimeout(startAutoplay, 3000) // Restart after 3 seconds
    }
  }
  
  // Handle carousel API
  function handleCarouselApi(api: any) {
    carouselApi = api
    if (api) {
      api.on('select', () => {
        currentSlide = api.selectedScrollSnap()
      })
    }
  }
  
  // Lifecycle
  onMount(() => {
    if (config.autoplay && autoplay !== false) {
      startAutoplay()
    }
  })
  
  onDestroy(() => {
    stopAutoplay()
  })
  
  // Watch for prop changes
  $effect(() => {
    if (config.autoplay && autoplay !== false) {
      startAutoplay()
    } else {
      stopAutoplay()
    }
  })
</script>

{#if variant === 'default'}
  <!-- Use original component -->
  <Banners {sliderBannersDesktop} {sliderBannersMobile} {...props} />
{:else if sliderBannersDesktop?.length > 0}
  <!-- Enhanced variant with full functionality -->
  <div class="{config.containerClass} enhanced-banners">
    <Carousel
      opts={{
        align: 'start',
        loop: true,
        duration: config.transition === 'fade' ? 1000 : 500
      }}
      class="w-full h-full"
      onApi={handleCarouselApi}
    >
      <CarouselContent class="h-full">
        {#each sliderBannersDesktop as banner, index (banner.id || index)}
          <CarouselItem class="h-full">
            <div class="relative w-full h-full">
              <!-- Banner Image -->
              {#if banner.image || banner.url}
                <img 
                  src={banner.image || banner.url} 
                  alt={banner.title || banner.alt || 'Banner'} 
                  class="{config.imageClass}"
                  loading={index === 0 ? 'eager' : 'lazy'}
                />
              {:else}
                <div class="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                  <div class="text-center text-gray-500">
                    <div class="text-6xl mb-4">🖼️</div>
                    <span class="text-lg">No Image Available</span>
                  </div>
                </div>
              {/if}
              
              <!-- Overlay Content -->
              {#if config.showOverlay && (banner.title || banner.subtitle || banner.buttonText || banner.heading || banner.description)}
                <div class="{config.overlayClass}">
                  <div class="{config.contentClass}">
                    {#if banner.title || banner.heading}
                      <h1 class="{config.titleClass}">
                        {banner.title || banner.heading}
                      </h1>
                    {/if}
                    
                    {#if banner.subtitle || banner.description}
                      <p class="{config.subtitleClass}">
                        {banner.subtitle || banner.description}
                      </p>
                    {/if}
                    
                    {#if banner.buttonText || banner.cta}
                      <Button 
                        class="{config.buttonClass}"
                        href={banner.buttonLink || banner.link || '#'}
                      >
                        {banner.buttonText || banner.cta || 'Learn More'}
                      </Button>
                    {/if}
                    
                    <!-- Additional CTA for luxury theme -->
                    {#if variant === 'luxury' && banner.secondaryButtonText}
                      <Button 
                        variant="outline"
                        class="ml-4 border-amber-300 text-amber-300 hover:bg-amber-300 hover:text-black"
                        href={banner.secondaryButtonLink || '#'}
                      >
                        {banner.secondaryButtonText}
                      </Button>
                    {/if}
                  </div>
                </div>
              {/if}
              
              <!-- Fashion theme decorative elements -->
              {#if variant === 'fashion'}
                <div class="absolute top-8 right-8 z-20">
                  <div class="w-16 h-16 rounded-full bg-gradient-to-r from-pink-400 to-purple-500 opacity-20 animate-pulse"></div>
                </div>
                <div class="absolute bottom-8 left-8 z-20">
                  <div class="w-12 h-12 rounded-full bg-gradient-to-r from-purple-400 to-pink-500 opacity-30 animate-bounce"></div>
                </div>
              {/if}
            </div>
          </CarouselItem>
        {/each}
      </CarouselContent>
      
      <!-- Navigation arrows -->
      {#if showArrows && sliderBannersDesktop.length > 1}
        <div class="absolute left-4 top-1/2 z-30 hidden -translate-y-1/2 transform md:flex">
          <CarouselPrevious 
            class="rounded-full bg-white/80 backdrop-blur-sm p-3 text-black shadow-lg hover:bg-white transition-all duration-200" 
            onclick={stopAutoplay}
          />
        </div>
        
        <div class="absolute right-4 top-1/2 z-30 hidden -translate-y-1/2 transform md:flex">
          <CarouselNext 
            class="rounded-full bg-white/80 backdrop-blur-sm p-3 text-black shadow-lg hover:bg-white transition-all duration-200"
            onclick={stopAutoplay}
          />
        </div>
      {/if}
      
      <!-- Slide indicators -->
      {#if showDots && sliderBannersDesktop.length > 1}
        <div class="absolute bottom-6 left-1/2 z-30 flex -translate-x-1/2 transform space-x-2">
          {#each sliderBannersDesktop as _, index}
            <button
              class="h-3 w-3 rounded-full transition-all duration-200 {
                index === currentSlide 
                  ? 'bg-white shadow-lg scale-125' 
                  : 'bg-white/50 hover:bg-white/75'
              }"
              onclick={() => goToSlide(index)}
              aria-label="Go to slide {index + 1}"
            ></button>
          {/each}
        </div>
      {/if}
    </Carousel>
  </div>
{:else}
  <!-- Fallback when no banners -->
  <div class="{config.containerClass} enhanced-banners">
    <div class="flex items-center justify-center h-64 bg-gray-100">
      <div class="text-center text-gray-500">
        <div class="text-6xl mb-4">🏪</div>
        <h3 class="text-xl font-medium mb-2">No banners configured</h3>
        <p class="text-sm">Add some banners to showcase your products</p>
      </div>
    </div>
  </div>
{/if}

<style>
  /* Enhanced banners global styles */
  .enhanced-banners :global(.carousel) {
    height: 100%;
  }
  
  .enhanced-banners :global(.carousel-content) {
    height: 100%;
  }
  
  .enhanced-banners :global(.carousel-item) {
    height: 100%;
  }
  
  /* Luxury theme styles */
  .luxury-hero {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  }
  
  .luxury-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #d4af37, transparent);
    z-index: 20;
  }
  
  /* Minimal theme styles */
  .minimal-hero {
    background: #f8f9fa;
  }
  
  /* Fashion theme styles */
  .fashion-hero {
    background: linear-gradient(135deg, #ff6b9d 0%, #c44569 100%);
    overflow: hidden;
  }
  
  .fashion-hero::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(236, 72, 153, 0.1) 0%, transparent 70%);
    animation: float 20s ease-in-out infinite;
    z-index: 1;
  }
  
  /* Tech theme styles */
  .tech-hero {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  }
  
  .tech-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(30, 64, 175, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
  }
  
  /* Animations */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-20px) rotate(180deg);
    }
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .luxury-hero,
    .minimal-hero,
    .fashion-hero,
    .tech-hero {
      min-height: 50vh;
    }
    
    .luxury-hero .text-center,
    .fashion-hero .text-center {
      padding: 0 1rem;
    }
    
    .minimal-hero .pl-16,
    .tech-hero .pl-16 {
      padding-left: 1rem;
    }
  }
  
  /* Hover effects */
  .enhanced-banners:hover .luxury-hero img {
    transform: scale(1.02);
  }
  
  .enhanced-banners:hover .fashion-hero img {
    transform: scale(1.05);
  }
</style>
