<script lang="ts">
  // Enhanced Product Grid Component - Product grids with theme variants
  
  import FeaturedProductsGrid from './featured-products-grid.svelte'
  import Product from './product-card.svelte'
  import { Button } from '$lib/components/ui/button'
  import { Skeleton } from '$lib/components/ui/skeleton'
  import { Grid, List, Filter, SortAsc, Star, TrendingUp } from 'lucide-svelte'
  
  let { 
    variant = 'default',
    theme = 'default',
    data = [],
    displayProduct,
    loadMore,
    loading = false,
    showFilters = false,
    layout = 'grid',
    itemsPerRow = 4,
    ...props 
  } = $props()
  
  // State
  let viewMode = $state(layout)
  let sortBy = $state('featured')
  let filterOpen = $state(false)
  
  // Variant configurations
  const variants = {
    luxury: {
      containerClass: 'luxury-products py-24 bg-gradient-to-b from-amber-50 to-white relative',
      headerClass: 'text-center mb-20 max-w-4xl mx-auto',
      titleClass: 'text-6xl font-serif font-bold text-gray-900 mb-6 tracking-wide',
      subtitleClass: 'text-2xl font-serif text-gray-600 max-w-3xl mx-auto leading-relaxed',
      gridClass: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10 max-w-7xl mx-auto px-8',
      masonryClass: 'masonry-grid max-w-7xl mx-auto px-8',
      loadMoreClass: 'mt-20 text-center',
      buttonClass: 'bg-gradient-to-r from-amber-400 to-amber-600 text-black px-12 py-4 font-bold uppercase tracking-widest hover:from-amber-500 hover:to-amber-700 transition-all duration-300 shadow-xl',
      cardVariant: 'luxury',
      showHeader: true,
      title: 'Curated Excellence',
      subtitle: 'Discover our handpicked selection of premium products, each chosen for its exceptional quality and timeless appeal'
    },
    minimal: {
      containerClass: 'minimal-products py-16 bg-white',
      headerClass: 'text-center mb-16 max-w-2xl mx-auto',
      titleClass: 'text-4xl font-sans font-light text-gray-900 mb-4',
      subtitleClass: 'text-lg font-sans text-gray-500',
      gridClass: 'grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 max-w-6xl mx-auto px-4',
      loadMoreClass: 'mt-16 text-center',
      buttonClass: 'bg-black text-white px-8 py-3 font-medium hover:bg-gray-800 transition-colors duration-200',
      cardVariant: 'minimal',
      showHeader: true,
      title: 'Products',
      subtitle: 'Simple. Quality. Essential.'
    },
    fashion: {
      containerClass: 'fashion-products py-28 bg-gradient-to-br from-pink-50 via-white to-purple-50 relative overflow-hidden',
      headerClass: 'text-center mb-24 max-w-5xl mx-auto',
      titleClass: 'text-7xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent mb-8 leading-tight',
      subtitleClass: 'text-2xl font-medium text-gray-700 max-w-3xl mx-auto',
      gridClass: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12 max-w-7xl mx-auto px-8',
      loadMoreClass: 'mt-24 text-center',
      buttonClass: 'bg-gradient-to-r from-pink-500 to-purple-600 text-white px-14 py-4 rounded-full font-bold hover:from-pink-600 hover:to-purple-700 transition-all duration-300 shadow-xl transform hover:scale-105',
      cardVariant: 'fashion',
      showHeader: true,
      title: 'Trending Now',
      subtitle: 'Stay ahead of the curve with our latest fashion finds and style essentials'
    },
    tech: {
      containerClass: 'tech-products py-20 bg-slate-50 relative',
      headerClass: 'text-center mb-18 max-w-4xl mx-auto',
      titleClass: 'text-5xl font-sans font-bold text-slate-900 mb-6',
      subtitleClass: 'text-xl font-sans text-slate-600',
      gridClass: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 max-w-7xl mx-auto px-6',
      loadMoreClass: 'mt-18 text-center',
      buttonClass: 'bg-blue-600 text-white px-10 py-3 font-semibold hover:bg-blue-700 transition-colors duration-200 border border-blue-500',
      cardVariant: 'tech',
      showHeader: true,
      title: 'Featured Products',
      subtitle: 'Cutting-edge technology and innovative solutions for modern life'
    },
    masonry: {
      containerClass: 'masonry-products py-20 bg-white',
      headerClass: 'text-center mb-16 max-w-3xl mx-auto',
      titleClass: 'text-5xl font-serif font-bold text-gray-900 mb-6',
      subtitleClass: 'text-lg font-serif text-gray-600',
      gridClass: 'masonry-grid max-w-7xl mx-auto px-6',
      loadMoreClass: 'mt-16 text-center',
      buttonClass: 'bg-gray-900 text-white px-8 py-3 font-medium hover:bg-gray-800 transition-colors duration-200',
      cardVariant: 'masonry',
      showHeader: true,
      title: 'Explore',
      subtitle: 'A curated selection of exceptional products'
    }
  }
  
  const config = variants[variant] || {
    containerClass: 'py-12',
    gridClass: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
    showHeader: false
  }
  
  // Loading skeleton count
  const skeletonCount = 12
  
  // Sort options
  const sortOptions = [
    { value: 'featured', label: 'Featured' },
    { value: 'newest', label: 'Newest' },
    { value: 'price-low', label: 'Price: Low to High' },
    { value: 'price-high', label: 'Price: High to Low' },
    { value: 'popular', label: 'Most Popular' }
  ]
  
  // View mode options
  const viewModes = [
    { value: 'grid', icon: Grid, label: 'Grid' },
    { value: 'list', icon: List, label: 'List' }
  ]
  
  // Get grid classes based on view mode
  function getGridClasses() {
    if (viewMode === 'list') {
      return 'flex flex-col gap-4 max-w-4xl mx-auto px-6'
    }
    if (variant === 'masonry') {
      return config.masonryClass
    }
    return config.gridClass
  }
</script>

{#if variant === 'default'}
  <!-- Use original component -->
  <FeaturedProductsGrid {data} {displayProduct} {loadMore} {loading} {...props} />
{:else}
  <!-- Enhanced variant -->
  <div class="{config.containerClass}">
    <!-- Background decorations -->
    {#if variant === 'fashion'}
      <div class="absolute top-20 right-10 w-40 h-40 rounded-full bg-gradient-to-r from-pink-400/10 to-purple-500/10 animate-pulse"></div>
      <div class="absolute bottom-32 left-10 w-32 h-32 rounded-full bg-gradient-to-r from-purple-400/10 to-pink-500/10 animate-bounce"></div>
    {/if}
    
    {#if variant === 'tech'}
      <div class="absolute inset-0 opacity-5">
        <div class="grid grid-cols-20 gap-2 h-full">
          {#each Array(100) as _, i}
            <div class="bg-blue-500 rounded-sm"></div>
          {/each}
        </div>
      </div>
    {/if}
    
    <div class="container mx-auto relative z-10">
      <!-- Header Section -->
      {#if config.showHeader}
        <div class="{config.headerClass}">
          <h2 class="{config.titleClass}">
            {config.title}
          </h2>
          {#if config.subtitle}
            <p class="{config.subtitleClass}">
              {config.subtitle}
            </p>
          {/if}
          
          <!-- Featured badges for luxury theme -->
          {#if variant === 'luxury'}
            <div class="flex items-center justify-center gap-6 mt-8 text-sm text-gray-600">
              <div class="flex items-center gap-2">
                <Star class="w-4 h-4 text-amber-500" />
                <span>Premium Quality</span>
              </div>
              <div class="flex items-center gap-2">
                <TrendingUp class="w-4 h-4 text-amber-500" />
                <span>Curated Selection</span>
              </div>
            </div>
          {/if}
        </div>
      {/if}
      
      <!-- Filters and View Controls -->
      {#if showFilters && data.length > 0}
        <div class="flex items-center justify-between mb-8 px-4">
          <div class="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onclick={() => filterOpen = !filterOpen}
            >
              <Filter class="w-4 h-4 mr-2" />
              Filters
            </Button>
            
            <select 
              bind:value={sortBy}
              class="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              {#each sortOptions as option}
                <option value={option.value}>{option.label}</option>
              {/each}
            </select>
          </div>
          
          <div class="flex items-center gap-2">
            {#each viewModes as mode}
              <button
                class="p-2 rounded-md {viewMode === mode.value ? 'bg-gray-200' : 'hover:bg-gray-100'}"
                onclick={() => viewMode = mode.value}
                aria-label={mode.label}
              >
                <svelte:component this={mode.icon} class="w-4 h-4" />
              </button>
            {/each}
          </div>
        </div>
      {/if}
      
      <!-- Products Grid -->
      {#if loading && data.length === 0}
        <!-- Loading skeleton -->
        <div class="{getGridClasses()}">
          {#each Array(skeletonCount) as _, i}
            <div class="space-y-4">
              <Skeleton class="aspect-square w-full" />
              <div class="space-y-2">
                <Skeleton class="h-4 w-3/4" />
                <Skeleton class="h-4 w-1/2" />
                <Skeleton class="h-6 w-1/3" />
              </div>
            </div>
          {/each}
        </div>
      {:else if data.length > 0}
        <!-- Products grid -->
        <div class="{getGridClasses()}">
          {#each data as product (product.id)}
            <div class="product-item {viewMode === 'list' ? 'flex gap-4 p-4 border rounded-lg' : ''}">
              <Product
                {product}
                aspectRatio={variant === 'masonry' ? 'auto' : viewMode === 'list' ? 'square' : 'square'}
                {displayProduct}
                variant={config.cardVariant}
                theme={theme}
                layout={viewMode}
              />
            </div>
          {/each}
        </div>
        
        <!-- Load More Button -->
        {#if loadMore}
          <div class="{config.loadMoreClass}">
            <Button 
              class="{config.buttonClass}"
              onclick={loadMore}
              disabled={loading}
            >
              {#if loading}
                <div class="flex items-center gap-2">
                  <div class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                  <span>Loading...</span>
                </div>
              {:else}
                {#if variant === 'luxury'}
                  Discover More Treasures
                {:else if variant === 'fashion'}
                  Show More Styles
                {:else if variant === 'tech'}
                  Load More Products
                {:else}
                  Load More Products
                {/if}
              {/if}
            </Button>
          </div>
        {/if}
      {:else}
        <!-- Empty state -->
        <div class="text-center py-20">
          <div class="mx-auto max-w-md">
            <div class="text-8xl mb-6">
              {#if variant === 'luxury'}
                💎
              {:else if variant === 'fashion'}
                👗
              {:else if variant === 'tech'}
                💻
              {:else}
                📦
              {/if}
            </div>
            <h3 class="text-3xl font-bold text-gray-900 mb-4">No products found</h3>
            <p class="text-lg text-gray-600 mb-8">
              {#if variant === 'luxury'}
                Our curators are working to bring you exceptional pieces
              {:else if variant === 'fashion'}
                New styles are coming soon to refresh your wardrobe
              {:else if variant === 'tech'}
                Innovative products will be available shortly
              {:else}
                Get started by adding some products to your store
              {/if}
            </p>
            <Button 
              href="/admin/products"
              class="{config.buttonClass}"
            >
              {#if variant === 'luxury'}
                Explore Collection
              {:else if variant === 'fashion'}
                Browse Styles
              {:else if variant === 'tech'}
                View Catalog
              {:else}
                Add Products
              {/if}
            </Button>
          </div>
        </div>
      {/if}
    </div>
  </div>
{/if}

<style>
  /* Masonry grid layout */
  .masonry-grid {
    column-count: 1;
    column-gap: 1.5rem;
  }
  
  @media (min-width: 640px) {
    .masonry-grid {
      column-count: 2;
    }
  }
  
  @media (min-width: 768px) {
    .masonry-grid {
      column-count: 3;
    }
  }
  
  @media (min-width: 1024px) {
    .masonry-grid {
      column-count: 4;
    }
  }
  
  .masonry-grid .product-item {
    break-inside: avoid;
    margin-bottom: 1.5rem;
  }
  
  /* Luxury theme styles */
  .luxury-products {
    position: relative;
  }
  
  .luxury-products::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #d4af37, transparent);
  }
  
  .luxury-products::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #d4af37, transparent);
  }
  
  /* Fashion theme styles */
  .fashion-products {
    position: relative;
    overflow: hidden;
  }
  
  .fashion-products::after {
    content: '';
    position: absolute;
    top: 20%;
    right: -10%;
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(236, 72, 153, 0.08) 0%, transparent 70%);
    border-radius: 50%;
    animation: float 20s ease-in-out infinite;
  }
  
  /* Tech theme styles */
  .tech-products {
    position: relative;
  }
  
  .tech-products::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(30, 64, 175, 0.03) 0%, transparent 50%);
    pointer-events: none;
  }
  
  /* Animations */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-40px) rotate(180deg);
    }
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .luxury-products,
    .minimal-products,
    .fashion-products,
    .tech-products,
    .masonry-products {
      padding: 3rem 0;
    }
    
    .masonry-grid {
      column-count: 1;
      column-gap: 1rem;
    }
    
    @media (min-width: 480px) {
      .masonry-grid {
        column-count: 2;
      }
    }
    
    .luxury-products .text-6xl {
      font-size: 3rem;
    }
    
    .fashion-products .text-7xl {
      font-size: 3.5rem;
    }
  }
  
  /* List view styles */
  .product-item.flex {
    transition: all 0.2s ease;
  }
  
  .product-item.flex:hover {
    shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
</style>
