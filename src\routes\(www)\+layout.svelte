<script lang="ts">
	import { setCartState } from '$lib/core/stores/cart.svelte'
	import { onMount, type Snippet } from 'svelte'
	import Nav from '$lib/components/nav/nav.svelte'
	import ThemeAwareNav from '$lib/components/nav/theme-aware-nav.svelte'
	import Footer from '$lib/components/common/footer.svelte'
	import { getGlobalThemeManager } from '$lib/themes'
	import { setProductState } from '$lib/core/stores/product.svelte'
	import BottomNav from '$lib/components/nav/bottom-nav.svelte'
	import StorePlugins from '$lib/core/components/plugins/store-plugins.svelte'
	import { page } from '$app/state'
	let { children, data }: { children: Snippet; data: { store: StoreData } } = $props()

	setCartState()
	setProductState()

	let currentTheme = $state('default')
	let themeManager: any = null

	onMount(() => {
		themeManager = getGlobalThemeManager()
		if (themeManager) {
			// Subscribe to theme changes
			const unsubscribe = themeManager.currentTheme.subscribe((theme: any) => {
				currentTheme = theme?.id || 'default'
			})

			return unsubscribe
		}
	})

	let isProductDetailsPage = $derived(page.route?.id === '/(www)/products/[slug]')
	let isProductsListingPage = $derived(page?.route?.id === '/(www)/products' || page?.route?.id === '/(www)/[slug]')
</script>

<div class="flex min-h-screen flex-col justify-between theme-layout theme-{currentTheme}">

	<ThemeAwareNav storeData={data.store} />
	<main class="min-h-screen">
		{@render children()}
	</main>
	<Footer />
	{#if !isProductDetailsPage}
		<BottomNav class={isProductsListingPage ? 'invisible' : ''} />
	{/if}
</div>

<StorePlugins storeData={data.store} />
