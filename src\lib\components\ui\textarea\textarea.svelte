<script lang="ts">
	import type { WithElementRef, WithoutChildren } from 'bits-ui'
	import type { HTMLTextareaAttributes } from 'svelte/elements'
	import { cn } from '$lib/core/utils'

	let {
		ref = $bindable(null),
		value = $bindable(),
		class: className,
		...restProps
	}: WithoutChildren<WithElementRef<HTMLTextareaAttributes>> = $props()
</script>

<textarea
	bind:this={ref}
	bind:value
	class={cn(
		'flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50',
		className
	)}
	{...restProps}
></textarea>
