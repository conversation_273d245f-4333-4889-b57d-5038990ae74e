// Complete TypeScript interfaces for the section-based theme system

export interface SectionConfig {
  id: string
  component: string
  variant: string
  theme?: string
  props?: Record<string, any>
  order: number
  visible: boolean
  responsive?: {
    mobile?: Partial<SectionConfig>
    tablet?: Partial<SectionConfig>
    desktop?: Partial<SectionConfig>
  }
}

export interface ThemeConfig {
  id: string
  name: string
  description: string
  category: string
  preview: string
  author?: string
  version?: string
  layout?: {
    header?: string
    footer?: string
    navigation?: string
  }
  sections: SectionConfig[]
  globalStyles: {
    fontFamily: string
    primaryColor: string
    secondaryColor: string
    accentColor?: string
    backgroundColor?: string
    textColor?: string
    borderRadius: string
    spacing?: string
    shadows?: string
  }
  customCSS?: string
  settings?: ThemeSettings
}

export interface ThemeSettings {
  layout?: {
    containerMaxWidth?: string
    sectionSpacing?: string
    contentPadding?: string
  }
  typography?: {
    headingFont?: string
    bodyFont?: string
    fontSize?: {
      small?: string
      medium?: string
      large?: string
      xlarge?: string
    }
  }
  colors?: {
    success?: string
    warning?: string
    error?: string
    info?: string
  }
  animations?: {
    enabled?: boolean
    duration?: string
    easing?: string
  }
}

export interface SectionVariant {
  id: string
  name: string
  description: string
  preview?: string
  props?: Record<string, any>
  styles?: Record<string, string>
  responsive?: boolean
}

export interface ComponentDefinition {
  id: string
  name: string
  description: string
  category: string
  variants: SectionVariant[]
  defaultVariant: string
  props?: Record<string, any>
  requiredProps?: string[]
}

export interface ThemeManagerState {
  currentTheme: ThemeConfig
  availableThemes: ThemeConfig[]
  isLoading: boolean
  error?: string
  previewMode?: boolean
  previewTheme?: ThemeConfig
}

export interface SectionData {
  page?: any
  featuredCategories?: any[]
  featuredProducts?: any[]
  collections?: any[]
  banners?: any[]
  loading?: boolean
  displayProduct?: (product: any) => void
  loadMore?: () => void
  [key: string]: any
}

export interface ThemePreferences {
  themeId: string
  customizations?: Record<string, any>
  sectionOrder?: string[]
  hiddenSections?: string[]
  lastModified?: string
}

// Event types for theme system
export interface ThemeChangeEvent {
  previousTheme: ThemeConfig
  newTheme: ThemeConfig
  timestamp: number
}

export interface SectionUpdateEvent {
  sectionId: string
  changes: Partial<SectionConfig>
  timestamp: number
}

// Utility types
export type ThemeCategory = 'luxury' | 'minimal' | 'fashion' | 'tech' | 'default' | 'custom'
export type SectionComponent = 'banners' | 'collections' | 'featured-products' | 'categories' | 'testimonials' | 'brands' | 'newsletter' | 'social-proof' | 'custom'
export type VariantType = 'luxury' | 'minimal' | 'fashion' | 'tech' | 'default' | 'custom'

// Component props interfaces
export interface BannerProps {
  sliderBannersDesktop?: any[]
  sliderBannersMobile?: any[]
  autoplay?: boolean
  showDots?: boolean
  showArrows?: boolean
  variant?: string
  theme?: string
}

export interface CollectionProps {
  collections?: any[]
  variant?: string
  theme?: string
  showDescription?: boolean
  itemsPerRow?: number
  layout?: 'grid' | 'carousel' | 'masonry'
}

export interface ProductGridProps {
  products?: any[]
  variant?: string
  theme?: string
  layout?: 'grid' | 'masonry' | 'list'
  itemsPerRow?: number
  showFilters?: boolean
  displayProduct?: (product: any) => void
  loadMore?: () => void
}

export interface CategoryProps {
  categories?: any[]
  variant?: string
  theme?: string
  layout?: 'grid' | 'list' | 'carousel'
  showImages?: boolean
}

// Theme validation
export interface ThemeValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

// Export all types
export type {
  SectionConfig,
  ThemeConfig,
  ThemeSettings,
  SectionVariant,
  ComponentDefinition,
  ThemeManagerState,
  SectionData,
  ThemePreferences,
  ThemeChangeEvent,
  SectionUpdateEvent,
  BannerProps,
  CollectionProps,
  ProductGridProps,
  CategoryProps,
  ThemeValidationResult
}
