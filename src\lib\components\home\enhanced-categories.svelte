<script lang="ts">
  // Enhanced Categories Component - Categories with theme variants
  
  import HomepageCategoryListWithImage from './homepage-category-list-with-image.svelte'
  import { Skeleton } from '$lib/components/ui/skeleton'
  import { Button } from '$lib/components/ui/button'
  import { ArrowRight, ShoppingBag, Star, TrendingUp } from 'lucide-svelte'
  
  let { 
    variant = 'default',
    theme = 'default',
    categories = [],
    loading = false,
    layout = 'grid',
    showImages = true,
    itemsPerRow = 4,
    ...props 
  } = $props()
  
  // Variant configurations
  const variants = {
    luxury: {
      containerClass: 'luxury-categories py-20 bg-gradient-to-b from-white to-amber-50 relative',
      headerClass: 'text-center mb-16 max-w-4xl mx-auto',
      titleClass: 'text-5xl font-serif font-bold text-gray-900 mb-6 tracking-wide',
      subtitleClass: 'text-xl font-serif text-gray-600 leading-relaxed',
      gridClass: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 max-w-7xl mx-auto px-8',
      categoryClass: 'group relative overflow-hidden rounded-none shadow-lg hover:shadow-2xl transition-all duration-500 bg-white border border-amber-200',
      imageClass: 'w-full h-48 object-cover transition-transform duration-500 group-hover:scale-105',
      contentClass: 'p-6 text-center',
      nameClass: 'font-serif text-xl font-semibold text-gray-900 mb-2 group-hover:text-amber-700 transition-colors',
      countClass: 'text-sm text-amber-600 font-medium',
      overlayClass: 'absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300',
      showHeader: true,
      showCount: true,
      title: 'Curated Categories',
      subtitle: 'Explore our carefully selected collections of premium products'
    },
    minimal: {
      containerClass: 'minimal-categories py-12 bg-white',
      headerClass: 'text-center mb-12 max-w-2xl mx-auto',
      titleClass: 'text-3xl font-sans font-light text-gray-900 mb-4',
      subtitleClass: 'text-lg font-sans text-gray-500',
      gridClass: 'grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 max-w-6xl mx-auto px-4',
      categoryClass: 'group relative overflow-hidden bg-white border border-gray-200 hover:border-gray-300 transition-all duration-200',
      imageClass: 'w-full h-40 object-cover',
      contentClass: 'p-4 text-center',
      nameClass: 'font-sans text-lg font-medium text-gray-900 mb-1',
      countClass: 'text-sm text-gray-500',
      overlayClass: 'absolute inset-0 bg-black/10 opacity-0 hover:opacity-100 transition-opacity duration-200',
      showHeader: true,
      showCount: false,
      title: 'Categories',
      subtitle: 'Browse by category'
    },
    fashion: {
      containerClass: 'fashion-categories py-24 bg-gradient-to-br from-pink-50 via-white to-purple-50 relative overflow-hidden',
      headerClass: 'text-center mb-20 max-w-5xl mx-auto',
      titleClass: 'text-6xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent mb-6 leading-tight',
      subtitleClass: 'text-2xl font-medium text-gray-700',
      gridClass: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10 max-w-7xl mx-auto px-8',
      categoryClass: 'group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 bg-white border border-pink-100',
      imageClass: 'w-full h-56 object-cover transition-transform duration-300 group-hover:scale-110',
      contentClass: 'p-6 text-center',
      nameClass: 'font-bold text-xl text-gray-900 mb-2 group-hover:bg-gradient-to-r group-hover:from-pink-600 group-hover:to-purple-600 group-hover:bg-clip-text group-hover:text-transparent transition-all',
      countClass: 'text-sm text-pink-600 font-medium',
      overlayClass: 'absolute inset-0 bg-gradient-to-t from-pink-500/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300',
      showHeader: true,
      showCount: true,
      title: 'Style Categories',
      subtitle: 'Discover your perfect style across our curated fashion collections'
    },
    tech: {
      containerClass: 'tech-categories py-16 bg-slate-50 relative',
      headerClass: 'text-center mb-14 max-w-4xl mx-auto',
      titleClass: 'text-4xl font-sans font-bold text-slate-900 mb-4',
      subtitleClass: 'text-xl font-sans text-slate-600',
      gridClass: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 max-w-7xl mx-auto px-6',
      categoryClass: 'group relative overflow-hidden rounded-lg shadow-sm hover:shadow-lg transition-all duration-200 bg-white border border-blue-200',
      imageClass: 'w-full h-44 object-cover bg-slate-100',
      contentClass: 'p-5 text-center',
      nameClass: 'font-sans text-lg font-semibold text-slate-900 mb-2 group-hover:text-blue-600 transition-colors',
      countClass: 'text-sm text-blue-600 font-medium',
      overlayClass: 'absolute inset-0 bg-blue-900/10 opacity-0 hover:opacity-100 transition-opacity duration-200',
      showHeader: true,
      showCount: true,
      title: 'Product Categories',
      subtitle: 'Browse our comprehensive range of technology products'
    }
  }
  
  const config = variants[variant] || {
    containerClass: 'py-12',
    gridClass: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
    showHeader: false
  }
  
  // Loading skeleton count
  const skeletonCount = 8
</script>

{#if variant === 'default'}
  <!-- Use original component -->
  <HomepageCategoryListWithImage {categories} {loading} {...props} />
{:else}
  <!-- Enhanced variant -->
  <div class="{config.containerClass}">
    <!-- Background decorations -->
    {#if variant === 'fashion'}
      <div class="absolute top-16 right-12 w-32 h-32 rounded-full bg-gradient-to-r from-pink-400/20 to-purple-500/20 animate-pulse"></div>
      <div class="absolute bottom-24 left-12 w-24 h-24 rounded-full bg-gradient-to-r from-purple-400/20 to-pink-500/20 animate-bounce"></div>
    {/if}
    
    {#if variant === 'tech'}
      <div class="absolute inset-0 opacity-5">
        <div class="grid grid-cols-16 gap-3 h-full">
          {#each Array(80) as _, i}
            <div class="bg-blue-500 rounded"></div>
          {/each}
        </div>
      </div>
    {/if}
    
    <div class="container mx-auto relative z-10">
      <!-- Header Section -->
      {#if config.showHeader}
        <div class="{config.headerClass}">
          <h2 class="{config.titleClass}">
            {config.title}
          </h2>
          {#if config.subtitle}
            <p class="{config.subtitleClass}">
              {config.subtitle}
            </p>
          {/if}
          
          <!-- Stats for luxury theme -->
          {#if variant === 'luxury' && categories.length > 0}
            <div class="flex items-center justify-center gap-8 mt-8 text-sm text-gray-600">
              <div class="flex items-center gap-2">
                <Star class="w-4 h-4 text-amber-500" />
                <span>{categories.length} Categories</span>
              </div>
              <div class="flex items-center gap-2">
                <TrendingUp class="w-4 h-4 text-amber-500" />
                <span>Premium Selection</span>
              </div>
            </div>
          {/if}
        </div>
      {/if}
      
      <!-- Categories Grid -->
      {#if loading}
        <!-- Loading skeleton -->
        <div class="{config.gridClass}">
          {#each Array(skeletonCount) as _, i}
            <div class="space-y-4">
              <Skeleton class="aspect-[4/3] w-full" />
              <div class="space-y-2 px-4">
                <Skeleton class="h-5 w-3/4 mx-auto" />
                <Skeleton class="h-4 w-1/2 mx-auto" />
              </div>
            </div>
          {/each}
        </div>
      {:else if categories.length > 0}
        <!-- Categories grid -->
        <div class="{config.gridClass}">
          {#each categories as category (category.id)}
            <a 
              href="/categories/{category.slug || category.id}"
              class="{config.categoryClass}"
            >
              <!-- Category Image -->
              {#if showImages && (category.image || category.thumbnail || category.img)}
                <div class="relative overflow-hidden">
                  <img 
                    src={category.image || category.thumbnail || category.img} 
                    alt={category.name || category.title}
                    class="{config.imageClass}"
                    loading="lazy"
                  />
                  <div class="{config.overlayClass}"></div>
                  
                  <!-- Fashion theme decorative elements -->
                  {#if variant === 'fashion'}
                    <div class="absolute top-4 right-4 w-8 h-8 rounded-full bg-gradient-to-r from-pink-400 to-purple-500 opacity-0 group-hover:opacity-70 transition-opacity duration-300"></div>
                  {/if}
                  
                  <!-- Luxury theme badge -->
                  {#if variant === 'luxury' && category.featured}
                    <div class="absolute top-4 left-4 bg-amber-500 text-black px-3 py-1 text-xs font-bold uppercase tracking-wider">
                      Featured
                    </div>
                  {/if}
                </div>
              {:else}
                <!-- Placeholder when no image -->
                <div class="w-full h-48 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                  <div class="text-center text-gray-500">
                    <ShoppingBag class="w-12 h-12 mx-auto mb-2" />
                    <span class="text-sm">Category</span>
                  </div>
                </div>
              {/if}
              
              <!-- Category Content -->
              <div class="{config.contentClass}">
                <h3 class="{config.nameClass}">
                  {category.name || category.title}
                </h3>
                
                {#if config.showCount && category.productCount}
                  <p class="{config.countClass}">
                    {category.productCount} {category.productCount === 1 ? 'product' : 'products'}
                  </p>
                {/if}
                
                {#if category.description && variant === 'luxury'}
                  <p class="text-sm text-gray-600 mt-2 line-clamp-2">
                    {category.description}
                  </p>
                {/if}
                
                <!-- View category button for fashion theme -->
                {#if variant === 'fashion'}
                  <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <Button 
                      size="sm"
                      class="bg-gradient-to-r from-pink-500 to-purple-600 text-white hover:from-pink-600 hover:to-purple-700"
                    >
                      <span>Explore</span>
                      <ArrowRight class="ml-2 w-3 h-3" />
                    </Button>
                  </div>
                {/if}
              </div>
            </a>
          {/each}
        </div>
        
        <!-- View All Categories Button -->
        {#if categories.length >= 8}
          <div class="text-center mt-12">
            <Button 
              href="/categories"
              class="{
                variant === 'luxury' ? 'bg-gradient-to-r from-amber-400 to-amber-600 text-black hover:from-amber-500 hover:to-amber-700 px-8 py-3 font-bold uppercase tracking-wider' :
                variant === 'fashion' ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white hover:from-pink-600 hover:to-purple-700 px-8 py-3 rounded-full font-bold' :
                variant === 'tech' ? 'bg-blue-600 text-white hover:bg-blue-700 px-6 py-3 font-semibold' :
                'bg-gray-900 text-white hover:bg-gray-800 px-6 py-3'
              }"
            >
              <span>View All Categories</span>
              <ArrowRight class="ml-2 w-4 h-4" />
            </Button>
          </div>
        {/if}
      {:else}
        <!-- Empty state -->
        <div class="text-center py-16">
          <div class="mx-auto max-w-md">
            <div class="text-6xl mb-6">
              {#if variant === 'luxury'}
                💎
              {:else if variant === 'fashion'}
                👗
              {:else if variant === 'tech'}
                💻
              {:else}
                📂
              {/if}
            </div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">No categories found</h3>
            <p class="text-gray-600 mb-6">
              {#if variant === 'luxury'}
                Create elegant categories to organize your premium products
              {:else if variant === 'fashion'}
                Build stylish categories to showcase your fashion collections
              {:else if variant === 'tech'}
                Organize your products into technical categories
              {:else}
                Get started by creating product categories
              {/if}
            </p>
            <Button 
              href="/admin/categories"
              class="{
                variant === 'luxury' ? 'bg-gradient-to-r from-amber-400 to-amber-600 text-black' :
                variant === 'fashion' ? 'bg-gradient-to-r from-pink-500 to-purple-600 text-white' :
                variant === 'tech' ? 'bg-blue-600 text-white' :
                'bg-gray-900 text-white'
              }"
            >
              Create Categories
            </Button>
          </div>
        </div>
      {/if}
    </div>
  </div>
{/if}

<style>
  /* Line clamp utility */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* Luxury theme styles */
  .luxury-categories {
    position: relative;
  }
  
  .luxury-categories::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #d4af37, transparent);
  }
  
  /* Fashion theme styles */
  .fashion-categories {
    position: relative;
    overflow: hidden;
  }
  
  .fashion-categories::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(236, 72, 153, 0.05) 0%, transparent 70%);
    animation: float 30s ease-in-out infinite;
    z-index: 0;
  }
  
  /* Tech theme styles */
  .tech-categories {
    position: relative;
  }
  
  .tech-categories::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #1e40af, #3b82f6);
  }
  
  /* Animations */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-25px) rotate(180deg);
    }
  }
  
  /* Hover effects */
  .luxury-categories a:hover {
    transform: translateY(-8px);
  }
  
  .fashion-categories a:hover {
    transform: translateY(-4px);
  }
  
  .tech-categories a:hover {
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15);
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .luxury-categories,
    .minimal-categories,
    .fashion-categories,
    .tech-categories {
      padding: 2rem 0;
    }
    
    .luxury-categories .text-5xl {
      font-size: 2.5rem;
    }
    
    .fashion-categories .text-6xl {
      font-size: 3rem;
    }
  }
</style>
