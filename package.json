{"name": "svelte-commerce", "version": "4.0.0", "description": "A modern, performant e-commerce platform built with SvelteKit. Featuring a headless architecture, server-side rendering, and a beautiful, responsive UI. Includes essential e-commerce features like product catalog, cart, checkout, and order management.", "type": "module", "license": "MIT", "keywords": ["ecommerce", "svelte", "sveltekit", "headless", "e-commerce", "storefront", "shopping-cart", "checkout", "ecommerce-platform", "tailwindcss", "typescript", "vite"], "author": "<PERSON><PERSON><PERSON><PERSON>hera", "repository": {"type": "git", "url": "https://github.com/itswadesh/svelte-commerce"}, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "start": "vite preview", "lint": "prettier --check . && eslint .", "format": "prettier --write .", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "db:seed": "cross-env DB_SEEDING=true tsx src/server/db/seed.ts", "db:migrate": "drizzle-kit push --config drizzle.config.ts && cross-env DB_SEEDING=true tsx src/server/db/seed.ts", "gen:manifest": "node ./generate-manifest.js", "test:wrapper": "./test-wrapper.js", "test": "bun test:wrapper", "test:ui": "bun test:wrapper --ui"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@misiki/litekart-connector": "^2.0.25", "@misiki/medusa-connector": "^2.1.0", "@playwright/test": "^1.50.1", "@sveltejs/adapter-auto": "^4.0.0", "@sveltejs/adapter-vercel": "^5.6.1", "@sveltejs/kit": "^2.17.1", "@sveltejs/vite-plugin-svelte": "^5.0.3", "@sveltejs/vite-plugin-svelte-inspector": "^4.0.1", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.13.1", "@unpic/svelte": "^1.0.0", "autoprefixer": "^10.4.20", "bits-ui": "^1.0.0-next.95", "clsx": "^2.1.1", "cmdk-sv": "^0.0.18", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "embla-carousel-auto-scroll": "^8.5.2", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-svelte": "^8.5.2", "embla-carousel-wheel-gestures": "^8.0.1", "formsnap": "^2.0.0-next.1", "hex-color-to-color-name": "^1.0.2", "postcss": "^8.4.49", "postcss-load-config": "^6.0.1", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "razorpay": "^2.9.5", "sharp": "^0.34.1", "svelte": "^5.19.8", "svelte-check": "^4.1.4", "svelte-confetti": "^2.3.1", "svelte-routing": "^2.13.0", "svelte-share-buttons-component": "^3.0.0", "svelte-sonner": "^0.3.28", "svelte-stripe": "^1.3.0", "sveltekit-superforms": "^2.21.1", "tailwind-merge": "^2.5.5", "tailwind-scrollbar": "^3.1.0", "tailwind-variants": "^0.3.0", "tailwindcss": "^3.4.16", "tailwindcss-animate": "^1.0.7", "tslib": "^2.8.1", "typescript": "^5.7.3", "vaul-svelte": "^0.3.2", "vitest": "^3.0.5"}, "dependencies": {"@internationalized/date": "^3.7.0", "fuse.js": "^7.1.0", "lucide-svelte": "^0.514.0", "paneforge": "^0.0.6", "shadcn-svelte": "^0.14.0", "stripe": "^17.6.0", "vite": "^6.1.0", "zod": "^3.24.1"}}