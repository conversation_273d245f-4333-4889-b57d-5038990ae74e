<script lang="ts">
  // Enhanced Featured Products Component - Products with theme variants and headers
  
  import FeaturedProductsGrid from './featured-products-grid.svelte'
  import EnhancedProductGrid from './enhanced-product-grid.svelte'
  import { Button } from '$lib/components/ui/button'
  import { ArrowRight, Sparkles, TrendingUp, Star } from 'lucide-svelte'
  
  let { 
    variant = 'default',
    theme = 'default',
    data = [],
    displayProduct,
    loadMore,
    hasMore = false,
    loading = false,
    showHeader = true,
    headerTitle = 'NEW ARRIVALS',
    showViewAll = true,
    viewAllText = 'View All',
    viewAllLink = '/products',
    layout = 'grid',
    ...props 
  } = $props()
  
  // Variant configurations
  const variants = {
    default: {
      containerClass: 'mx-2 lg:container lg:mx-auto',
      headerClass: 'mx-auto mb-2 flex items-center justify-center',
      titleClass: 'mx-4 text-[20px] font-bold tracking-wide',
      borderClass: 'flex-1 border-2 border-black dark:border-white',
      viewAllClass: 'mx-auto mb-5 flex items-center justify-center text-[12px]',
      viewAllLinkClass: 'mx-auto block h-fit border-b border-black pb-[2px] dark:border-white',
      showBorders: true,
      titleIcon: null
    },
    luxury: {
      containerClass: 'mx-2 lg:container lg:mx-auto',
      headerClass: 'text-center mb-8',
      titleClass: 'text-3xl font-serif font-bold text-amber-600 mb-4',
      subtitleClass: 'text-lg text-gray-600 font-light',
      viewAllClass: 'flex justify-center mb-8',
      viewAllLinkClass: 'bg-gradient-to-r from-amber-400 to-amber-600 text-black px-8 py-3 font-bold uppercase tracking-widest hover:from-amber-500 hover:to-amber-700 transition-all duration-300 shadow-lg border-2 border-amber-300',
      showBorders: false,
      titleIcon: Star,
      subtitle: 'Discover our carefully curated collection of premium products'
    },
    minimal: {
      containerClass: 'mx-2 lg:container lg:mx-auto',
      headerClass: 'text-center mb-12',
      titleClass: 'text-2xl font-light text-gray-900 mb-6',
      viewAllClass: 'flex justify-center mb-8',
      viewAllLinkClass: 'bg-black text-white px-6 py-2 font-medium hover:bg-gray-800 transition-colors duration-200 border border-black',
      showBorders: false,
      titleIcon: null
    },
    fashion: {
      containerClass: 'mx-2 lg:container lg:mx-auto',
      headerClass: 'text-center mb-10',
      titleClass: 'text-4xl font-bold bg-gradient-to-r from-pink-500 to-purple-600 bg-clip-text text-transparent mb-4',
      subtitleClass: 'text-lg text-gray-600',
      viewAllClass: 'flex justify-center mb-8',
      viewAllLinkClass: 'bg-gradient-to-r from-pink-500 to-purple-600 text-white px-10 py-3 rounded-full font-bold hover:from-pink-600 hover:to-purple-700 transition-all duration-300 shadow-xl transform hover:scale-105',
      showBorders: false,
      titleIcon: Sparkles,
      subtitle: 'Stay ahead of the trends with our latest fashion collection'
    },
    tech: {
      containerClass: 'mx-2 lg:container lg:mx-auto',
      headerClass: 'text-center mb-8',
      titleClass: 'text-3xl font-bold text-blue-600 mb-4',
      subtitleClass: 'text-lg text-gray-600',
      viewAllClass: 'flex justify-center mb-8',
      viewAllLinkClass: 'bg-blue-600 text-white px-8 py-3 font-semibold hover:bg-blue-700 transition-colors duration-200 border border-blue-500 rounded-md',
      showBorders: false,
      titleIcon: TrendingUp,
      subtitle: 'Cutting-edge technology products for the modern world'
    }
  }
  
  const config = variants[variant] || variants.default
</script>

<div class="{config.containerClass}">
  {#if showHeader}
    <!-- Header Section -->
    {#if variant === 'default' && config.showBorders}
      <!-- Default theme with borders -->
      <div class="{config.headerClass}">
        <div class="{config.borderClass}"></div>
        <div class="{config.titleClass}">{headerTitle}</div>
        <div class="{config.borderClass}"></div>
      </div>
      
      {#if showViewAll}
        <div class="{config.viewAllClass}">
          <a href={viewAllLink} class="{config.viewAllLinkClass}">{viewAllText}</a>
        </div>
      {/if}
    {:else}
      <!-- Enhanced themes -->
      <div class="{config.headerClass}">
        <h2 class="{config.titleClass}">
          {#if config.titleIcon}
            <svelte:component this={config.titleIcon} class="inline-block w-8 h-8 mr-3" />
          {/if}
          {headerTitle}
        </h2>
        
        {#if config.subtitle}
          <p class="{config.subtitleClass}">{config.subtitle}</p>
        {/if}
      </div>
      
      {#if showViewAll}
        <div class="{config.viewAllClass}">
          <Button 
            href={viewAllLink}
            class="{config.viewAllLinkClass}"
          >
            {viewAllText}
            <ArrowRight class="ml-2 w-4 h-4" />
          </Button>
        </div>
      {/if}
    {/if}
  {/if}

  <!-- Products Grid -->
  {#if variant === 'default'}
    <!-- Use original featured products grid for default -->
    <FeaturedProductsGrid 
      {data} 
      {displayProduct} 
      {loadMore} 
      {hasMore} 
      {loading} 
      {...props} 
    />
  {:else}
    <!-- Use enhanced product grid for themed variants -->
    <EnhancedProductGrid 
      {variant}
      {theme}
      {data} 
      {displayProduct} 
      {loadMore} 
      {hasMore} 
      {loading} 
      {layout}
      {...props} 
    />
  {/if}

  <!-- Load More Button for enhanced themes -->
  {#if variant !== 'default' && hasMore && !loading}
    <div class="mt-8 flex justify-center">
      <Button 
        onclick={loadMore}
        class="bg-gray-100 text-gray-800 px-8 py-3 font-medium hover:bg-gray-200 transition-colors duration-200 border border-gray-300"
      >
        Load More Products
        <ArrowRight class="ml-2 w-4 h-4" />
      </Button>
    </div>
  {/if}
</div>

<style>
  /* Enhanced featured products styles */
  :global(.enhanced-featured-products) {
    position: relative;
  }
  
  /* Theme-specific header styles */
  :global(.theme-luxury .enhanced-featured-products h2) {
    font-family: 'Playfair Display', serif;
  }
  
  :global(.theme-minimal .enhanced-featured-products h2) {
    font-weight: 300;
  }
  
  :global(.theme-fashion .enhanced-featured-products h2) {
    font-family: 'Poppins', sans-serif;
  }
  
  :global(.theme-tech .enhanced-featured-products h2) {
    font-family: 'Inter', sans-serif;
  }
</style>
