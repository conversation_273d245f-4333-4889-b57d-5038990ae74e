// Theme System Main Export - Complete theme system exports

// Core theme system
import { ThemeManager } from './theme-manager'
import SectionManager from './section-manager.svelte'
export { ThemeManager } from './theme-manager'
export { default as SectionManager } from './section-manager.svelte'

// Theme configurations
import {
  availableThemes,
  themeCategories,
  getThemeById,
  getThemesByCategory,
  getDefaultTheme
} from './theme-configs'

export {
  availableThemes,
  defaultThemeConfig,
  luxuryThemeConfig,
  minimalThemeConfig,
  fashionThemeConfig,
  techThemeConfig,
  themeCategories,
  getThemeById,
  getThemesByCategory,
  getDefaultTheme
} from './theme-configs'

// TypeScript types
export type {
  ThemeConfig,
  SectionConfig,
  ThemeSettings,
  SectionVariant,
  ComponentDefinition,
  ThemeManagerState,
  SectionData,
  ThemePreferences,
  ThemeChangeEvent,
  SectionUpdateEvent,
  BannerProps,
  CollectionProps,
  ProductGridProps,
  CategoryProps,
  ThemeValidationResult,
  ThemeCategory,
  SectionComponent,
  VariantType
} from './types'

// Theme components
import ThemeSelector from '$lib/components/theme/theme-selector.svelte'
export { default as ThemeSelector } from '$lib/components/theme/theme-selector.svelte'

// Enhanced section components
export { default as EnhancedBanners } from '$lib/components/home/<USER>'
export { default as EnhancedCollections } from '$lib/components/home/<USER>'
export { default as EnhancedProductGrid } from '$lib/components/product-catalogue/enhanced-product-grid.svelte'
export { default as EnhancedCategories } from '$lib/components/home/<USER>'

// Theme system utilities
export class ThemeSystemUtils {
  /**
   * Initialize the theme system with default configuration
   */
  static initializeThemeSystem(defaultThemeId?: string) {
    const themeManager = new ThemeManager(availableThemes, defaultThemeId)
    
    // Apply initial theme styles
    themeManager.applyGlobalStyles()
    
    return themeManager
  }
  
  /**
   * Get theme-specific component variant
   */
  static getComponentVariant(theme: string, component: string): string {
    const themeConfig = getThemeById(theme)
    if (!themeConfig) return 'default'
    
    const section = themeConfig.sections.find(s => s.component === component)
    return section?.variant || 'default'
  }
  
  /**
   * Check if theme supports a specific component
   */
  static themeSupportsComponent(theme: string, component: string): boolean {
    const themeConfig = getThemeById(theme)
    if (!themeConfig) return false
    
    return themeConfig.sections.some(s => s.component === component && s.visible)
  }
  
  /**
   * Get all visible sections for a theme
   */
  static getThemeSections(theme: string): SectionConfig[] {
    const themeConfig = getThemeById(theme)
    if (!themeConfig) return []
    
    return themeConfig.sections
      .filter(section => section.visible)
      .sort((a, b) => a.order - b.order)
  }
  
  /**
   * Validate theme configuration
   */
  static validateTheme(theme: ThemeConfig): { isValid: boolean; errors: string[] } {
    const errors: string[] = []
    
    // Basic validation
    if (!theme.id) errors.push('Theme ID is required')
    if (!theme.name) errors.push('Theme name is required')
    if (!theme.sections) errors.push('Theme sections are required')
    if (!theme.globalStyles) errors.push('Theme global styles are required')
    
    // Sections validation
    if (theme.sections) {
      theme.sections.forEach((section, index) => {
        if (!section.id) errors.push(`Section ${index} is missing ID`)
        if (!section.component) errors.push(`Section ${index} is missing component`)
        if (!section.variant) errors.push(`Section ${index} is missing variant`)
        if (typeof section.order !== 'number') errors.push(`Section ${index} is missing order`)
      })
    }
    
    // Global styles validation
    if (theme.globalStyles) {
      if (!theme.globalStyles.fontFamily) errors.push('Font family is required')
      if (!theme.globalStyles.primaryColor) errors.push('Primary color is required')
      if (!theme.globalStyles.secondaryColor) errors.push('Secondary color is required')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }
  
  /**
   * Create a custom theme based on an existing theme
   */
  static createCustomTheme(baseTheme: string, customizations: Partial<ThemeConfig>): ThemeConfig {
    const base = getThemeById(baseTheme)
    if (!base) throw new Error(`Base theme "${baseTheme}" not found`)
    
    return {
      ...base,
      ...customizations,
      id: customizations.id || `${base.id}-custom`,
      name: customizations.name || `${base.name} (Custom)`,
      globalStyles: {
        ...base.globalStyles,
        ...customizations.globalStyles
      },
      sections: customizations.sections || base.sections
    }
  }
  
  /**
   * Get theme CSS variables
   */
  static getThemeCSSVariables(theme: ThemeConfig): Record<string, string> {
    const styles = theme.globalStyles
    
    return {
      '--theme-font-family': styles.fontFamily,
      '--theme-primary-color': styles.primaryColor,
      '--theme-secondary-color': styles.secondaryColor,
      '--theme-accent-color': styles.accentColor || styles.primaryColor,
      '--theme-background-color': styles.backgroundColor || '#ffffff',
      '--theme-text-color': styles.textColor || '#1f2937',
      '--theme-border-radius': styles.borderRadius,
      '--theme-spacing': styles.spacing || '1rem',
      '--theme-shadows': styles.shadows || '0 1px 3px 0 rgb(0 0 0 / 0.1)'
    }
  }
  
  /**
   * Apply theme CSS variables to document
   */
  static applyThemeVariables(theme: ThemeConfig): void {
    const variables = this.getThemeCSSVariables(theme)
    const root = document.documentElement
    
    Object.entries(variables).forEach(([property, value]) => {
      root.style.setProperty(property, value)
    })
    
    // Apply theme class to body
    document.body.className = document.body.className.replace(/theme-\w+/g, '')
    document.body.classList.add(`theme-${theme.id}`)
  }
  
  /**
   * Get responsive section props
   */
  static getResponsiveSectionProps(section: SectionConfig, screenSize: 'mobile' | 'tablet' | 'desktop'): Record<string, any> {
    if (!section.responsive) return section.props || {}
    
    const responsiveProps = section.responsive[screenSize]
    if (!responsiveProps) return section.props || {}
    
    return {
      ...section.props,
      ...responsiveProps.props
    }
  }
  
  /**
   * Generate theme preview data
   */
  static generateThemePreview(theme: ThemeConfig): {
    colors: string[]
    fontFamily: string
    borderRadius: string
    sectionCount: number
    category: string
  } {
    return {
      colors: [
        theme.globalStyles.primaryColor,
        theme.globalStyles.secondaryColor,
        theme.globalStyles.accentColor || theme.globalStyles.primaryColor
      ],
      fontFamily: theme.globalStyles.fontFamily.split(',')[0].trim(),
      borderRadius: theme.globalStyles.borderRadius,
      sectionCount: theme.sections.filter(s => s.visible).length,
      category: theme.category
    }
  }
}

// Global theme manager instance (singleton)
let globalThemeManager: ThemeManager | null = null

/**
 * Get or create the global theme manager instance
 */
export function getGlobalThemeManager(): ThemeManager {
  if (!globalThemeManager) {
    globalThemeManager = ThemeSystemUtils.initializeThemeSystem()
  }
  return globalThemeManager
}

/**
 * Initialize theme system on app startup
 */
export function initializeThemeSystem(defaultThemeId?: string): ThemeManager {
  globalThemeManager = ThemeSystemUtils.initializeThemeSystem(defaultThemeId)
  return globalThemeManager
}

/**
 * Cleanup theme system
 */
export function cleanupThemeSystem(): void {
  if (globalThemeManager) {
    globalThemeManager.destroy()
    globalThemeManager = null
  }
}

// Re-export theme configurations for convenience
export {
  availableThemes as themes,
  themeCategories as categories
} from './theme-configs'

// Default export - removed to avoid import issues
// Use named exports instead: import { ThemeManager, SectionManager, etc. } from '$lib/themes'
